// 全局变量
let currentPage = 1;
let totalPages = 1;
let pageSize = 10;
let currentRecordId = null;
let searchQuery = '';
let searchCategory = 'all';
let tempUnitPrice = 0;
let tempOperationType = '';
let tempConfig = '';
let isDarkMode = true; // 默认开启深色模式
let selectedBuchaType = 'normal'; // 默认选择普通补差

// 身份验证相关函数
function getToken() {
    return localStorage.getItem('token');
}

// 检查是否已登录
function isAuthenticated() {
    return !!getToken();
}

// 检查数据分析页面访问权限
async function checkAnalyticsAccess() {
    const token = getToken();
    if (!token) {
        showNotification('请先登录', 'error');
        setTimeout(() => {
            window.location.href = '/login.html?redirect=' + encodeURIComponent('/analytics-enhanced.html');
        }, 1500);
        return false;
    }

    try {
        const response = await fetch('/api/validate-token', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Token验证失败');
        }

        const data = await response.json();
        if (!data.valid) {
            throw new Error('Token无效');
        }

        if (data.user.role !== 'admin') {
            showNotification('权限不足：只有管理员才能访问数据分析页面', 'error');
            return false;
        }

        // 管理员可以访问
        return true;

    } catch (error) {
        console.error('权限检查失败:', error);
        showNotification('权限验证失败，请重新登录', 'error');
        setTimeout(() => {
            localStorage.removeItem('token');
            sessionStorage.removeItem('token');
            window.location.href = '/login.html?redirect=' + encodeURIComponent('/analytics-enhanced.html');
        }, 1500);
        return false;
    }
}

// 添加主题切换功能
function toggleDarkMode() {
    isDarkMode = !isDarkMode;
    document.body.classList.toggle('dark-mode', isDarkMode);
    localStorage.setItem('darkMode', isDarkMode ? 'true' : 'false');
    
    // 更新主题图标
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.innerHTML = isDarkMode ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
    }
    
    // 如果有图表，重新初始化图表以应用新主题
    if (dailyAmountChart || timeDistributionChart || partTypeChart || operationTypeChart || dailyAOVChart) {
        initializeCharts();
        loadAnalysisData();
    }
}

// 切换补差类型
function toggleBuchaType(type) {
    selectedBuchaType = type;
    const normalBtn = document.getElementById('normalBuchaBtn');
    const bucha200Btn = document.getElementById('bucha200Btn');
    const largeBtn = document.getElementById('largeBuchaBtn');
    
    // 重置所有按钮
    normalBtn.style.backgroundColor = '';
    bucha200Btn.style.backgroundColor = '';
    largeBtn.style.backgroundColor = '';
    
    // 高亮选中的按钮
    if (type === 'normal') {
        normalBtn.style.backgroundColor = 'var(--clay-primary)';
    } else if (type === 'bucha200') {
        bucha200Btn.style.backgroundColor = 'var(--clay-primary)';
    } else { // large
        largeBtn.style.backgroundColor = 'var(--clay-primary)';
    }
    
    // 如果有输入值，自动计算
    const amountInput = document.getElementById('buchaAmount');
    if (amountInput && amountInput.value && parseInt(amountInput.value) > 0) {
        calculateBucha();
    }
}

// 初始化主题
function initTheme() {
    // 从localStorage读取主题设置
    const savedDarkMode = localStorage.getItem('darkMode');
    if (savedDarkMode !== null) {
        isDarkMode = savedDarkMode === 'true';
    }
    
    // 应用主题
    document.body.classList.toggle('dark-mode', isDarkMode);
    
    // 更新主题图标
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.innerHTML = isDarkMode ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
        themeToggle.addEventListener('click', toggleDarkMode);
    }
}

// 发送带有授权头的请求
async function sendAuthenticatedRequest(url, options = {}) {
    const token = getToken();
    if (!token) {
        // 如果没有令牌，重定向到登录页面
        window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
        return null;
    }
    
    // 合并选项，添加授权头
    const authOptions = { 
        ...options,
        headers: {
            ...options.headers,
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    };
    
    try {
        const response = await fetch(url, authOptions);
        
        // 如果返回401状态码，可能是令牌已过期，重定向到登录页面
        if (response.status === 401) {
            localStorage.removeItem('token');
            window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
            return null;
        }
        
        return response;
    } catch (error) {
        console.error('请求错误:', error);
        showToast('网络请求失败', 'error');
        throw error;
    }
}

// 原始表格数据相关的全局变量
let rawTableCurrentPage = 1;
let rawTableTotalPages = 1;
let rawTablePageSize = 10;
let currentRawTableId = null;
let rawTableSearchQuery = '';
let rawTableSearchCategory = 'all';
let currentRawTableData = '';

// 简单的日期格式化函数
function formatDate(dateString) {
    if (!dateString) return '未知日期';
    try {
        const date = new Date(dateString);
        return date.toLocaleString();
    } catch (error) {
        console.error('日期格式化错误:', error);
        return dateString;
    }
}

// 格式容错处理函数
function normalizeLineFormat(line, lineNumber) {
    // 去除首尾空白字符
    line = line.trim();

    // 跳过空行
    if (!line) {
        return null;
    }

    // 尝试多种格式匹配和修正
    let match;

    // 标准格式1：+100 更换 内存条 或 -50 减配 显卡 (整数)
    match = line.match(/^([+-])(\d+)\s+(\S+)\s+(.+)$/);
    if (match) {
        return {
            price: parseInt(match[2], 10) * (match[1] === '-' ? -1 : 1),
            operation_type: match[3],
            description: match[4].trim(),
            original: line,
            corrected: false
        };
    }

    // 标准格式2：+269.00 价格 内存条 或 -50.50 减配 显卡 (小数)
    match = line.match(/^([+-])(\d+(?:\.\d+)?)\s+(\S+)\s+(.+)$/);
    if (match) {
        return {
            price: Math.round(parseFloat(match[2]) * (match[1] === '-' ? -1 : 1)),
            operation_type: match[3],
            description: match[4].trim(),
            original: line,
            corrected: false
        };
    }

    // 容错1：缺少符号的正数 - 100 更换 内存条 (整数)
    match = line.match(/^(\d+)\s+(\S+)\s+(.+)$/);
    if (match) {
        const correctedLine = `+${match[1]} ${match[2]} ${match[3]}`;
        return {
            price: parseInt(match[1], 10),
            operation_type: match[2],
            description: match[3].trim(),
            original: line,
            corrected: correctedLine
        };
    }

    // 容错2：缺少符号的正数 - 269.00 价格 内存条 (小数)
    match = line.match(/^(\d+(?:\.\d+)?)\s+(\S+)\s+(.+)$/);
    if (match) {
        const correctedLine = `+${match[1]} ${match[2]} ${match[3]}`;
        return {
            price: Math.round(parseFloat(match[1])),
            operation_type: match[2],
            description: match[3].trim(),
            original: line,
            corrected: correctedLine
        };
    }

    // 容错3：符号和数字之间有空格 - + 100 更换 内存条 (整数)
    match = line.match(/^([+-])\s+(\d+)\s+(\S+)\s+(.+)$/);
    if (match) {
        const correctedLine = `${match[1]}${match[2]} ${match[3]} ${match[4]}`;
        return {
            price: parseInt(match[2], 10) * (match[1] === '-' ? -1 : 1),
            operation_type: match[3],
            description: match[4].trim(),
            original: line,
            corrected: correctedLine
        };
    }

    // 容错4：符号和数字之间有空格 - + 269.00 价格 内存条 (小数)
    match = line.match(/^([+-])\s+(\d+(?:\.\d+)?)\s+(\S+)\s+(.+)$/);
    if (match) {
        const correctedLine = `${match[1]}${match[2]} ${match[3]} ${match[4]}`;
        return {
            price: Math.round(parseFloat(match[2]) * (match[1] === '-' ? -1 : 1)),
            operation_type: match[3],
            description: match[4].trim(),
            original: line,
            corrected: correctedLine
        };
    }

    // 容错5：操作类型包含空格 - +100 更 换 内存条 (整数)
    match = line.match(/^([+-])(\d+)\s+(.+)$/);
    if (match) {
        const parts = match[3].trim().split(/\s+/);
        if (parts.length >= 2) {
            // 假设第一个词是操作类型，其余是描述
            const operationType = parts[0];
            const description = parts.slice(1).join(' ');
            const correctedLine = `${match[1]}${match[2]} ${operationType} ${description}`;
            return {
                price: parseInt(match[2], 10) * (match[1] === '-' ? -1 : 1),
                operation_type: operationType,
                description: description,
                original: line,
                corrected: correctedLine
            };
        }
    }

    // 容错6：操作类型包含空格 - +269.00 价 格 内存条 (小数)
    match = line.match(/^([+-])(\d+(?:\.\d+)?)\s+(.+)$/);
    if (match) {
        const parts = match[3].trim().split(/\s+/);
        if (parts.length >= 2) {
            // 假设第一个词是操作类型，其余是描述
            const operationType = parts[0];
            const description = parts.slice(1).join(' ');
            const correctedLine = `${match[1]}${match[2]} ${operationType} ${description}`;
            return {
                price: Math.round(parseFloat(match[2]) * (match[1] === '-' ? -1 : 1)),
                operation_type: operationType,
                description: description,
                original: line,
                corrected: correctedLine
            };
        }
    }

    // 如果所有格式都不匹配，返回错误信息
    return {
        error: true,
        lineNumber: lineNumber,
        content: line,
        message: `第${lineNumber}行格式错误："${line}"`
    };
}

// 自动计算总价
function autoCalculate() {
    const inputText = document.getElementById('inputText').value;
    const lines = inputText.split('\n');
    let total = 0;

    lines.forEach((line, index) => {
        const result = normalizeLineFormat(line, index + 1);

        // 跳过空行和错误行
        if (result === null || result.error) {
            return;
        }

        // 累加价格
        total += result.price;
    });

    const totalAmountEl = document.getElementById('totalAmount');
    if (totalAmountEl.textContent !== String(total)) {
        // 添加更新动画类
        totalAmountEl.classList.add('updating');

        // 数字计数动画效果
        const oldValue = parseInt(totalAmountEl.textContent) || 0;
        const newValue = total;

        if (oldValue !== newValue) {
            animateNumber(totalAmountEl, oldValue, newValue, 500);
        }

        // 移除更新动画类
        setTimeout(() => {
            totalAmountEl.classList.remove('updating');
        }, 400);

        // 使用 Animate.css
        animateCSS('#totalAmount', 'heartBeat');
    }
}

// 数字动画函数
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const difference = end - start;

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.round(start + (difference * easeOutQuart));

        element.textContent = current;

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }

    requestAnimationFrame(updateNumber);
}

// 显示气泡通知 - 增强动画版本
function showToast(message, type = 'success', duration = 3000) {
    const toastId = `toast-${Date.now()}`;
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast ${type} animate__animated`;

    // 添加图标
    const icon = document.createElement('i');
    if (type === 'success') {
        icon.className = 'fas fa-check-circle';
    } else if (type === 'error') {
        icon.className = 'fas fa-exclamation-circle';
    } else if (type === 'info') {
        icon.className = 'fas fa-info-circle';
    } else {
        icon.className = 'fas fa-exclamation-circle';
    }

    // 添加图标动画
    icon.classList.add('animate__animated', 'animate__bounceIn');
    icon.style.animationDelay = '0.2s';
    toast.appendChild(icon);

    // 添加消息文本 - 支持多行文本
    const text = document.createElement('span');
    text.style.whiteSpace = 'pre-line'; // 支持换行符显示
    text.style.wordBreak = 'break-word'; // 支持长单词换行
    text.textContent = message;
    text.classList.add('animate__animated', 'animate__fadeIn');
    text.style.animationDelay = '0.3s';
    toast.appendChild(text);

    // 添加关闭按钮
    const closeBtn = document.createElement('i');
    closeBtn.className = 'fas fa-times';
    closeBtn.style.cursor = 'pointer';
    closeBtn.style.fontSize = '14px';
    closeBtn.style.opacity = '0.7';
    closeBtn.style.transition = 'all 0.3s var(--bounce-transition)';
    closeBtn.onmouseover = () => {
        closeBtn.style.transform = 'rotate(90deg) scale(1.2)';
        closeBtn.style.opacity = '1';
    };
    closeBtn.onmouseout = () => {
        closeBtn.style.transform = 'rotate(0) scale(1)';
        closeBtn.style.opacity = '0.7';
    };
    closeBtn.onclick = () => {
        closeToast(toast);
    };
    toast.appendChild(closeBtn);

    // 获取现有的通知，以便垂直堆叠
    const existingToasts = document.querySelectorAll('.toast');
    const offset = existingToasts.length * 15; // 每个通知向下偏移15px
    toast.style.top = `${20 + offset}px`;

    // 对于错误和信息类型的通知，增加最大宽度以容纳更多内容
    if (type === 'error' || type === 'info') {
        toast.style.maxWidth = '400px';
        toast.style.minWidth = '300px';
    }

    // 添加进入动画
    toast.classList.add('animate__fadeInRight');

    // 添加悬停效果
    toast.onmouseover = () => {
        toast.style.transform = 'translateY(-2px) scale(1.02)';
        toast.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';
    };
    toast.onmouseout = () => {
        toast.style.transform = 'translateY(0) scale(1)';
        toast.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
    };

    document.body.appendChild(toast);

    // Force reflow to ensure CSS animation works
    void toast.offsetWidth;

    // Show the toast with animation
    toast.classList.add('show');

    // Hide and remove after delay
    const timeoutId = setTimeout(() => {
        closeToast(toast);
    }, duration);

    // 保存timeout ID，以便在手动关闭时清除
    toast.dataset.timeoutId = timeoutId;
}

// 关闭气泡通知
function closeToast(toast) {
    // 清除自动关闭的计时器
    if (toast.dataset.timeoutId) {
        clearTimeout(parseInt(toast.dataset.timeoutId));
    }

    // 移除之前的动画类
    toast.classList.remove('animate__fadeInRight', 'animate__animated');

    // 添加退出动画
    toast.classList.add('animate__animated', 'animate__fadeOutRight');

    setTimeout(() => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);

            // 重新排列剩余的通知
            const remainingToasts = document.querySelectorAll('.toast');
            remainingToasts.forEach((t, index) => {
                t.style.top = `${20 + index * 15}px`;

                // 添加微小的动画效果
                t.style.transition = 'top 0.3s var(--bounce-transition)';
            });
        }
    }, 500);
}

function showInputError(element, message) {
    const errorDiv = document.getElementById(`${element.id}-error`);
    if (errorDiv) {
        errorDiv.textContent = message;
    }
    element.classList.add('input-error');
}

function clearInputErrors() {
    const errorMessages = document.querySelectorAll('.error-message');
    errorMessages.forEach(msg => msg.textContent = '');
    
    const inputs = [document.getElementById('price'), document.getElementById('config')];
    inputs.forEach(input => {
        if(input) input.classList.remove('input-error');
    });
}

// 添加新行
function addNewLine() {
    clearInputErrors(); // 首先清除旧的错误提示
    const priceInput = document.getElementById('price');
    const operationType = document.getElementById('operationType').value;
    const configInput = document.getElementById('config');
    const config = configInput.value;
    
    let price = priceInput.value;

    // 验证输入
    if (!price) {
        showInputError(priceInput, '请输入有效价格');
        return;
    }

    if (isNaN(parseInt(price, 10))) {
        showInputError(priceInput, '价格必须是数字');
        return;
    }
    
    price = parseInt(price, 10);

    if (operationType === '减配' && price > 0) {
        price = -price;
    }
    
    if (operationType !== '减配' && price < 0) {
        showInputError(priceInput, '只有"减配"类型才能输入负数价格');
        return;
    }

    if (!config) {
        showInputError(configInput, '请输入配置描述');
        return;
    }
    
    // 构建新行
    const sign = price >= 0 ? '+' : '';
    const newLine = `${sign}${price} ${operationType} ${config}`;
    const inputText = document.getElementById('inputText');
    
    // 添加到文本区域
    if (inputText.value && inputText.value.trim() !== '') {
        inputText.value += '\n' + newLine;
    } else {
        inputText.value = newLine;
    }
    
    // 清空输入框为下一次输入做准备
    document.getElementById('price').value = '';
    document.getElementById('config').value = '';
    document.getElementById('operationType').value = '更换'; // 自动重置下拉框
    document.getElementById('price').focus();
    
    // 自动计算总额
    autoCalculate();
    
    // 显示添加成功提示
    showToast('已添加项目', 'success');
}

// 重置表单
function resetForm() {
    document.getElementById('inputText').value = '';
    document.getElementById('price').value = '';
    document.getElementById('config').value = '';
    document.getElementById('totalAmount').textContent = '0';
    document.getElementById('operationType').value = '更换';
    document.getElementById('rawInput').value = '';

    showToast('已重置', 'success');
}

// 复制到剪贴板并自动保存
function copyToClipboard() {
    const inputText = document.getElementById('inputText').value;
    const total = document.getElementById('totalAmount').textContent;

    if (!inputText.trim()) {
        showToast('没有内容可复制', 'error');
        return;
    }

    const resultText = inputText + '\n合计: ' + total;
    const tempInput = document.createElement('textarea');
    tempInput.value = resultText;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand('copy');
    document.body.removeChild(tempInput);

    showToast('已复制到剪贴板', 'success');
    
    // 复制后自动保存
    saveToDatabase();
    
    // 复制并保存后自动重置
    document.getElementById('inputText').value = '';
    document.getElementById('price').value = '';
    document.getElementById('config').value = '';
    document.getElementById('totalAmount').textContent = '0';
    document.getElementById('operationType').value = '更换';
    document.getElementById('rawInput').value = '';
    
    showToast('已自动清空内容', 'success');
}

// 保存到数据库
async function saveToDatabase() {
    const inputText = document.getElementById('inputText').value;
    const total = document.getElementById('totalAmount').textContent;

    if (!inputText.trim()) {
        showToast('没有内容可保存', 'error');
        return;
    }

    // 解析输入文本为项目数组
    const lines = inputText.split('\n');
    const items = [];
    const errors = [];
    const corrections = [];

    lines.forEach((line, index) => {
        const result = normalizeLineFormat(line, index + 1);

        if (result === null) {
            // 空行，跳过
            return;
        }

        if (result.error) {
            errors.push(result);
            return;
        }

        // 记录修正信息
        if (result.corrected) {
            corrections.push({
                lineNumber: index + 1,
                original: result.original,
                corrected: result.corrected
            });
        }

        items.push({
            price: result.price,
            operation_type: result.operation_type,
            description: result.description
        });
    });

    // 如果有格式错误，显示详细错误信息
    if (errors.length > 0) {
        let errorMessage = '发现格式错误：\n';
        errors.forEach(error => {
            errorMessage += `• ${error.message}\n`;
        });
        errorMessage += '\n正确格式示例：\n+100 更换 内存条\n-50 减配 显卡';

        showToast(errorMessage, 'error', 8000); // 显示8秒
        return;
    }

    // 如果没有有效项目
    if (items.length === 0) {
        showToast('没有找到有效的价格项目', 'error');
        return;
    }

    // 如果有自动修正，提示用户
    if (corrections.length > 0) {
        let correctionMessage = '已自动修正格式：\n';
        corrections.slice(0, 3).forEach(correction => { // 最多显示3个修正
            correctionMessage += `• 第${correction.lineNumber}行：${correction.original} → ${correction.corrected}\n`;
        });
        if (corrections.length > 3) {
            correctionMessage += `• 还有${corrections.length - 3}行被自动修正\n`;
        }
        showToast(correctionMessage, 'info', 5000);
    }
    
    // 显示保存中提示
    showToast('保存中...', 'success');
    
    try {
        // 发送到后端
        const response = await sendAuthenticatedRequest('/api/save-price-record', {
            method: 'POST',
            body: JSON.stringify({
                total_amount: parseInt(total),
                items: items
            })
        });
        
        if (!response) {
            showToast('身份验证失败', 'error');
            return;
        }
        
        const data = await response.json();
        if (data.success) {
            showToast('保存成功', 'success');
            searchRecords(true); // 刷新历史记录
        } else {
            showToast('保存失败: ' + (data.message || '未知错误'), 'error');
            console.error('保存失败:', data.message);
        }
    } catch (error) {
        showToast('保存失败: ' + error.message, 'error');
        console.error('保存错误:', error);
    }
}

// 搜索历史记录
function searchRecords(silent = false) {
    searchQuery = document.getElementById('searchInput').value;
    searchCategory = document.getElementById('searchType').value;
    currentPage = 1;
    loadHistoryPage(silent);
}

// 重置历史记录搜索
function resetHistorySearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('searchType').value = 'all';
    searchQuery = '';
    searchCategory = 'all';
    currentPage = 1;
    loadHistoryPage();
    showToast('已重置搜索', 'success');
}

// 加载历史页面
async function loadHistoryPage(silent = false) {
    const historyList = document.getElementById('historyList');
    
    if (!silent) {
        historyList.innerHTML = `<tr><td colspan="${getTableColspan()}" style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> 加载中...</td></tr>`;
    }
    
    const url = `/api/price-records?page=${currentPage}&limit=${pageSize}&search=${encodeURIComponent(searchQuery)}&category=${searchCategory}`;
    
    try {
        const response = await sendAuthenticatedRequest(url, { method: 'GET' });
        if (!response) return; // 如果sendAuthenticatedRequest返回null，表示身份验证问题
        
        const data = await response.json();
        const records = data.records;
        totalPages = Math.ceil(data.total / pageSize);
        
        // 更新分页信息
        document.getElementById('recordCount').textContent = `总记录: ${data.total}`;
        document.getElementById('currentPage').textContent = `第${currentPage}页 / 共${totalPages}页`;
        document.getElementById('prevPage').disabled = currentPage <= 1;
        document.getElementById('nextPage').disabled = currentPage >= totalPages;
        
        historyList.innerHTML = ''; // Clear list
        
        if (records.length === 0) {
            historyList.innerHTML = `<tr><td colspan="${getTableColspan()}" style="text-align: center; padding: 20px;">暂无记录</td></tr>`;
            return;
        }
        
        const fragment = document.createDocumentFragment();
        
        // 获取每条记录的项目数量 - 此部分已由后端完成，可以直接使用 record.item_count
        for (const [index, record] of records.entries()) {
            const date = new Date(record.created_at).toLocaleString();
            
            const tr = document.createElement('tr');
            tr.style.borderBottom = '1px solid var(--border-color)';
            tr.style.transition = 'background-color 0.2s';
            // 使用 animate.css 并添加延迟
            tr.className = 'animate__animated animate__fadeInUp';
            tr.style.animationDelay = `${index * 0.05}s`;
            
            // 根据用户角色决定是否显示删除按钮
            const deleteButton = isCurrentUserAdmin() ?
                `<button onclick="deleteRecord(${record.id})" class="action-btn" title="删除" style="background-color: #ff4757;">
                    <i class="fas fa-trash-alt"></i>
                </button>` : '';

            // 根据用户角色决定是否显示创建者列
            const creatorCell = isCurrentUserAdmin() ?
                `<td style="padding: 10px; text-align: center;" data-label="创建者">
                    <span style="background: var(--clay-container-bg); padding: 2px 8px; border-radius: 12px; font-size: 12px;">
                        ${record.created_by || '未知用户'}
                    </span>
                </td>` : '';

            // 添加响应式类处理 - 直接使用 item_count
            tr.innerHTML = `
                <td style="padding: 10px;" data-label="ID">${record.id}</td>
                <td style="padding: 10px;" data-label="日期">${date}</td>
                <td style="padding: 10px;" data-label="配件数量">${record.item_count}</td>
                <td style="padding: 10px; text-align: right;" data-label="总金额">¥${record.total_amount}</td>
                ${creatorCell}
                <td style="padding: 10px; text-align: center;">
                    <div style="display: flex; justify-content: center; gap: 5px;">
                        <button onclick="viewRecordDetail(${record.id})" class="action-btn" title="查看">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="applyRecord(${record.id})" class="action-btn" title="应用此数据">
                            <i class="fas fa-download"></i>
                        </button>
                        ${deleteButton}
                    </div>
                </td>`;
            fragment.appendChild(tr);
        }
        
        historyList.appendChild(fragment);
    } catch (error) {
        historyList.innerHTML = `<tr><td colspan="${getTableColspan()}" style="text-align: center; padding: 20px;">加载失败</td></tr>`;
        console.error('Error:', error);
    }
}

// 切换页码
function changePage(delta) {
    currentPage += delta;
    if (currentPage < 1) currentPage = 1;
    if (currentPage > totalPages) currentPage = totalPages;
    loadHistoryPage();
}

// 查看记录详情
async function viewRecordDetail(recordId) {
    currentRecordId = recordId;
    const detailModal = document.getElementById('detailModal');
    const recordDetail = document.getElementById('recordDetail');
    recordDetail.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>';
    detailModal.style.display = 'block';
    
    // 防止背景滚动
    document.body.style.overflow = 'hidden';
    
    try {
        const response = await sendAuthenticatedRequest(`/api/price-records/${recordId}`, { method: 'GET' });
        if (!response) {
            recordDetail.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--error-color);">身份验证失败</div>';
            return;
        }
        
        const data = await response.json();
        const date = new Date(data.record.created_at).toLocaleString();
        let html = `
            <div style="margin-bottom: 15px; display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center;">
                <span style="margin-bottom: 5px;">创建时间: ${date}</span>
                <span style="font-size: 18px; font-weight: bold; color: var(--button-color); margin-bottom: 5px;">总金额: ¥${data.record.total_amount}</span>
            </div>
            <div style="background: var(--input-bg-color); border-radius: 8px; padding: 15px;">`;
        
        data.items.forEach(item => {
            const sign = item.price >= 0 ? '+' : '';
            html += `<div style="margin-bottom: 8px; display: flex; justify-content: space-between; flex-wrap: wrap;">
                <span style="word-break: break-all;">${sign}${item.price} ${item.operation_type} ${item.description}</span>
            </div>`;
        });
        
        html += '</div>';
        recordDetail.innerHTML = html;
        document.getElementById('detailTitle').textContent = `记录详情 (ID: ${recordId})`;
    } catch (error) {
        recordDetail.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--error-color);">加载失败</div>';
        console.error('Error:', error);
    }
}

// 关闭详情弹窗
function closeDetailModal() {
    document.getElementById('detailModal').style.display = 'none';
    // 恢复背景滚动
    document.body.style.overflow = 'auto';
    
    // 重置详情弹窗按钮状态
    const actionButton = document.getElementById('detailActionButton');
    if (actionButton) {
        actionButton.textContent = '应用此记录';
        actionButton.onclick = function() {
            loadSelectedRecord();
        };
    }
}

// 加载所选记录到当前表单
async function loadSelectedRecord() {
    if (!currentRecordId) return;

    try {
        const response = await sendAuthenticatedRequest(`/api/price-records/${currentRecordId}`, { method: 'GET' });
        if (!response) {
            showToast('身份验证失败', 'error');
            return;
        }

        const data = await response.json();

        // 清空当前输入
        document.getElementById('inputText').value = '';

        // 将历史记录的内容填充到输入框
        let text = '';
        data.items.forEach(item => {
            const sign = item.price >= 0 ? '+' : '';
            text += `${sign}${item.price} ${item.operation_type} ${item.description}\n`;
        });

        document.getElementById('inputText').value = text.trim();
        document.getElementById('totalAmount').textContent = data.record.total_amount;

        // 关闭弹窗
        closeDetailModal();
        showToast('已应用历史记录', 'success');
    } catch (error) {
        showToast('加载记录失败', 'error');
        console.error('Error:', error);
    }
}

// 直接应用记录数据（不需要打开详情弹窗）
async function applyRecord(recordId) {
    if (!recordId) return;

    try {
        const response = await sendAuthenticatedRequest(`/api/price-records/${recordId}`, { method: 'GET' });
        if (!response) {
            showToast('身份验证失败', 'error');
            return;
        }

        const data = await response.json();

        // 获取当前输入框内容
        const inputText = document.getElementById('inputText');
        const currentValue = inputText.value.trim();

        // 将历史记录的内容追加到输入框
        let newText = '';
        data.items.forEach(item => {
            const sign = item.price >= 0 ? '+' : '';
            newText += `${sign}${item.price} ${item.operation_type} ${item.description}\n`;
        });

        // 如果当前有内容，则追加；否则直接设置
        if (currentValue) {
            inputText.value = currentValue + '\n' + newText.trim();
        } else {
            inputText.value = newText.trim();
        }

        // 重新计算总额
        autoCalculate();

        showToast('已应用历史记录', 'success');
    } catch (error) {
        showToast('加载记录失败', 'error');
        console.error('Error:', error);
    }
}

// 函数已删除

// 设置表格排序功能
function setupTableSorting() {
    const table = document.querySelector('.editable-table');
    if (!table) return;
    
    const headers = table.querySelectorAll('thead th');
    // 最后一列（操作列）不需要排序
    for (let i = 0; i < headers.length - 1; i++) {
        const header = headers[i];
        header.style.cursor = 'pointer';
        header.title = '点击排序';
        header.innerHTML += ' <i class="fas fa-sort" style="opacity: 0.3;"></i>';
        
        header.addEventListener('click', function() {
            sortTable(i);
        });
    }
}

// 表格排序函数
function sortTable(columnIndex) {
    const table = document.querySelector('.editable-table');
    if (!table) return;
    
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr')).filter(tr => !tr.querySelector('button[title="添加行"]')); // 排除添加按钮行
    
    // 获取当前排序方向
    const header = table.querySelectorAll('thead th')[columnIndex];
    const isAsc = header.classList.contains('sort-asc');
    
    // 重置所有表头的排序图标
    table.querySelectorAll('thead th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
        th.querySelector('i').className = 'fas fa-sort';
        th.querySelector('i').style.opacity = '0.3';
    });
    
    // 设置当前表头的排序图标
    header.classList.add(isAsc ? 'sort-desc' : 'sort-asc');
    header.querySelector('i').className = isAsc ? 'fas fa-sort-down' : 'fas fa-sort-up';
    header.querySelector('i').style.opacity = '1';
    
    // 排序行
    rows.sort((a, b) => {
        const aValue = a.querySelectorAll('td')[columnIndex].textContent.trim();
        const bValue = b.querySelectorAll('td')[columnIndex].textContent.trim();
        
        // 如果是数字则进行数值比较
        if (!isNaN(parseFloat(aValue)) && !isNaN(parseFloat(bValue))) {
            return isAsc ? parseFloat(bValue) - parseFloat(aValue) : parseFloat(aValue) - parseFloat(bValue);
        }
        
        // 否则进行字符串比较
        return isAsc ? bValue.localeCompare(aValue, 'zh-CN') : aValue.localeCompare(bValue, 'zh-CN');
    });
    
    // 重新插入排序后的行
    const lastRow = tbody.querySelector('tr:last-child'); // 获取添加按钮所在的行
    tbody.innerHTML = ''; // 清空表格体
    
    // 添加排序后的行
    rows.forEach(row => {
        tbody.appendChild(row);
    });
    
    // 添加回最后一行（添加按钮行）
    tbody.appendChild(lastRow);
    
    showToast('表格已排序', 'success');
}

// 设置单元格数据验证
function setupCellValidation() {
    const table = document.querySelector('.editable-table');
    if (!table) return;
    
    // 为所有单元格添加数据验证
    const cells = table.querySelectorAll('td[contenteditable="true"]');
    cells.forEach((cell, index) => {
        // 数字列（成本、差价、补差）验证为数字
        const columnIndex = parseInt(cell.dataset.col);
        if (columnIndex >= 3) { // 第4、5、6列是数字列
            cell.addEventListener('input', function() {
                validateNumericCell(this);
                // 如果是差价列（索引4）或补差列（索引5），更新创收列
                if (columnIndex === 4 || columnIndex === 5) {
                    updateRevenueCell(this);
                }
            });

            cell.addEventListener('blur', function() {
                formatNumericCell(this);
                // 如果是差价列（索引4）或补差列（索引5），更新创收列
                if (columnIndex === 4 || columnIndex === 5) {
                    updateRevenueCell(this);
                }
            });
        }
    });
}

// 更新创收列
function updateRevenueCell(cell) {
    const row = cell.closest('tr');
    if (!row) return;

    // 获取同一行的差价和补差单元格
    const chajiaCell = row.querySelector('td[data-col="4"]'); // 差价列
    const buchaCell = row.querySelector('td[data-col="5"]'); // 补差列
    const revenueCell = row.querySelector('td[data-col="6"]'); // 创收列

    if (!chajiaCell || !buchaCell || !revenueCell) return;

    // 计算创收 = 补差 - 差价
    const chajiaStr = chajiaCell.textContent.trim();
    const buchaStr = buchaCell.textContent.trim();
    const chajia = chajiaStr !== '' ? parseFloat(chajiaStr) : 0;
    const bucha = buchaStr !== '' ? parseFloat(buchaStr) : 0;

    // 只有当补差或差价不为空时才计算创收
    let revenue = '';
    if (buchaStr !== '' || chajiaStr !== '') {
        revenue = bucha - chajia;
    }

    // 更新创收列的值
    revenueCell.textContent = revenue;

    // 添加动画效果
    revenueCell.style.backgroundColor = '#e8f5e8';
    setTimeout(() => {
        revenueCell.style.backgroundColor = '';
    }, 1000);
}

// 验证数字单元格
function validateNumericCell(cell) {
    const value = cell.textContent.trim();
    // 允许空值或数字（可以有小数点）
    if (value !== '' && isNaN(parseFloat(value))) {
        cell.classList.add('invalid-cell');
    } else {
        cell.classList.remove('invalid-cell');
    }
}

// 格式化数字单元格
function formatNumericCell(cell) {
    let value = cell.textContent.trim();
    if (value === '') return;
    
    // 尝试转换为数字
    const num = parseFloat(value);
    if (!isNaN(num)) {
        // 如果是整数，则不显示小数位
        if (Number.isInteger(num)) {
            cell.textContent = num;
        } else {
            // 否则保留两位小数
            cell.textContent = num.toFixed(2);
        }
    }
}

// 计算原始表格中"补差"列的总金额
function calculateRawTableTotal(rawData) {
    if (!rawData || typeof rawData !== 'string' || rawData.trim() === '') {
        return 0;
    }

    const lines = rawData.trim().split('\n');
    let total = 0;

    // 遍历每一行数据
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line === '') continue;

        // 分割数据行
        const cells = line.split('\t');

        // 补差列是第6列（索引为5）
        if (cells.length > 5) {
            const buchaStr = cells[5] ? cells[5].trim() : '';
            if (buchaStr !== '') {
                const bucha = parseFloat(buchaStr);
                if (!isNaN(bucha)) {
                    total += bucha;
                }
            }
        }
    }

    return Math.round(total); // 返回整数
}

// 保存编辑后的表格并转换数据
async function saveTableAndConvert() {
    // 从可编辑表格中提取数据
    const editedTableData = getEditedTableData();
    
    if (!editedTableData) {
        showToast('表格数据为空', 'error');
        return;
    }
    
    // 隐藏表格编辑区域
    hideTableEditor();
    
    // 转换为价格条目
    const convertResult = convertPastedText(editedTableData);
    const inputText = document.getElementById('inputText');

    // 将转换后的数据追加到输入框
    if (convertResult && convertResult.items) {
        const convertedItems = convertResult.items; // 不包含合计的条目
        const convertedWithTotal = convertResult.withTotal; // 包含合计的完整内容

        if (inputText.value && inputText.value.trim() !== '') {
            inputText.value += '\n' + convertedItems;
        } else {
            inputText.value = convertedItems;
        }

        // 自动计算
        autoCalculate();

        // 自动复制转换后的数据（包含合计）到剪贴板
        try {
            if (navigator.clipboard && window.isSecureContext) {
                // 使用现代 Clipboard API
                await navigator.clipboard.writeText(convertedWithTotal);
                showToast('转换成功并已复制到剪贴板', 'success');
            } else {
                // 降级方案：使用传统方法
                const tempTextarea = document.createElement('textarea');
                tempTextarea.value = convertedWithTotal;
                tempTextarea.style.position = 'fixed';
                tempTextarea.style.left = '-999999px';
                tempTextarea.style.top = '-999999px';
                document.body.appendChild(tempTextarea);
                tempTextarea.focus();
                tempTextarea.select();

                if (document.execCommand('copy')) {
                    showToast('转换成功并已复制到剪贴板', 'success');
                } else {
                    showToast('转换成功，但复制到剪贴板失败', 'error');
                }

                document.body.removeChild(tempTextarea);
            }
        } catch (copyError) {
            console.error('复制到剪贴板失败:', copyError);
            showToast('转换成功，但复制到剪贴板失败', 'error');
        }
    } else {
        showToast('转换结果为空，请检查数据格式', 'error');
    }
    
    // 将数据保存到数据库
    try {
        // 计算原始表格中"补差"列的总金额
        const total = calculateRawTableTotal(editedTableData);

        // 发送到API保存
        const response = await sendAuthenticatedRequest('/api/raw-table-data', {
            method: 'POST',
            body: JSON.stringify({
                total_amount: total,
                raw_data: editedTableData
            })
        });

        if (!response) {
            showToast('身份验证失败', 'error');
            return;
        }

        const data = await response.json();
        if (data.success) {
            showToast('原始数据已保存到数据库', 'success');
            // 清空输入框
            document.getElementById('rawInput').value = '';
            
            // 将焦点移至结果区域
            document.getElementById('inputText').scrollIntoView({ behavior: 'smooth' });
        } else {
            showToast('保存原始数据失败: ' + (data.message || '未知错误'), 'error');
            console.error('保存原始数据失败:', data.message);
        }
        
    } catch (saveError) {
        console.warn('保存原始数据出错:', saveError);
        showToast('保存原始数据失败: ' + saveError.message, 'error');
    }
}

// 创建可编辑表格
function createEditableTable(rawData, fixedHeaders = null) {
    if (!rawData || typeof rawData !== 'string' || rawData.trim() === '') {
        return '<p>无有效数据</p>';
    }
    
    const lines = rawData.trim().split('\n');
    if (lines.length === 0) {
        return '<p>无有效数据行</p>';
    }
    
    let tableHtml = '<table class="detail-table editable-table">';
    
    // 处理表头 - 使用固定表头或从数据中提取
    let headers = [];
    if (fixedHeaders) {
        headers = fixedHeaders;
    } else {
        headers = lines[0].split('\t');
    }
    
    // 添加表头行
    tableHtml += '<thead><tr>';
    for (let i = 0; i < headers.length; i++) {
        const header = headers[i] ? headers[i].trim() : `列${i+1}`;
        tableHtml += `<th>${header}</th>`;
    }
    // 添加操作列
    tableHtml += '<th>操作</th>';
    tableHtml += '</tr></thead>';
    
    // 处理表格内容
    tableHtml += '<tbody>';
    const startLine = fixedHeaders ? 0 : 1; // 如果使用固定表头，则从第0行开始处理数据
    for (let i = startLine; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line === '') continue;
        
        // 分割数据行
        const cells = line.split('\t');
        const rowNum = i - startLine + 1; // 行号从1开始
        tableHtml += `<tr data-row="${rowNum}">`;
        
        // 动态填充单元格，确保单元格数量与表头一致
        for (let j = 0; j < headers.length; j++) {
            let cellValue = '';
            let isEditable = true;

            // 如果是创收列（最后一列），计算创收值
            if (fixedHeaders && j === headers.length - 1 && headers[j] === '创收') {
                // 创收 = 补差 - 差价
                const buchaStr = cells[5] ? cells[5].trim() : '';
                const chajiaStr = cells[4] ? cells[4].trim() : '';
                const bucha = buchaStr !== '' ? parseFloat(buchaStr) : 0;
                const chajia = chajiaStr !== '' ? parseFloat(chajiaStr) : 0;

                // 只有当补差或差价不为空时才计算创收
                if (buchaStr !== '' || chajiaStr !== '') {
                    cellValue = bucha - chajia;
                } else {
                    cellValue = '';
                }
                isEditable = false; // 创收列不可编辑
            } else {
                // 其他列使用原始数据
                cellValue = j < cells.length ? cells[j] : '';
            }

            // 判断是否是数字，如果是则右对齐
            const isNumeric = !isNaN(parseFloat(cellValue)) && isFinite(cellValue);
            const cellClass = isNumeric ? 'numeric' : '';

            // 创建单元格，创收列不可编辑
            const editableAttr = isEditable ? 'contenteditable="true"' : 'contenteditable="false" style="background-color: #f5f5f5; color: #666;"';
            tableHtml += `<td class="${cellClass}" ${editableAttr} data-col="${j}" data-row-num="${rowNum}">${cellValue}</td>`;
        }
        
        // 添加操作按钮
        tableHtml += `<td>
            <button onclick="deleteTableRow(this)" class="action-btn" title="删除行">
                <i class="fas fa-trash-alt"></i>
            </button>
        </td>`;
        
        tableHtml += '</tr>';
    }
    
    // 添加新行按钮和清空表格按钮
    tableHtml += `
        <tr>
            <td colspan="${headers.length + 1}" style="text-align: center; padding: 15px;">
                <div style="display: flex; justify-content: center; gap: 10px;">
                    <button onclick="addTableRow()" class="button add-button" style="min-width: 120px;">
                        <i class="fas fa-plus"></i> 添加行
                    </button>
                    <button onclick="clearTable()" class="button reset-button" style="min-width: 120px;">
                        <i class="fas fa-trash"></i> 清空表格
                    </button>
                </div>
            </td>
        </tr>
    `;
    
    tableHtml += '</tbody></table>';
    
    return tableHtml;
}

// 删除表格行
function deleteTableRow(button) {
    const row = button.closest('tr');
    if (row) {
        row.remove();
        showToast('已删除行', 'success');
    }
}

// 清空表格
function clearTable() {
    if (confirm('确定要清空表格吗？此操作不可撤销。')) {
        const table = document.querySelector('.editable-table');
        if (!table) return;
        
        // 获取表格的所有行，但排除最后一行（添加行按钮所在的行）
        const rows = Array.from(table.querySelectorAll('tbody tr'));
        if (rows.length <= 1) return; // 如果只有添加行按钮所在的行，则不需要清空
        
        // 删除所有数据行，保留最后一行（添加行按钮所在的行）
        for (let i = 0; i < rows.length - 1; i++) {
            rows[i].remove();
        }
        
        showToast('表格已清空', 'success');
    }
}

// 添加表格行
function addTableRow() {
    const table = document.querySelector('.editable-table');
    if (!table) return;
    
    const headerCells = table.querySelectorAll('thead th');
    const tbody = table.querySelector('tbody');
    const lastRow = tbody.querySelector('tr:last-child');
    
    // 计算新行的行号 - 获取当前最大行号并加1
    let maxRowNum = 0;
    const rows = tbody.querySelectorAll('tr');
    rows.forEach(row => {
        const firstCell = row.querySelector('td[data-row-num]');
        if (firstCell) {
            const rowNum = parseInt(firstCell.dataset.rowNum);
            if (!isNaN(rowNum) && rowNum > maxRowNum) {
                maxRowNum = rowNum;
            }
        }
    });
    const newRowNum = maxRowNum + 1;
    
    // 创建新行
    const newRow = document.createElement('tr');
    newRow.dataset.row = newRowNum;
    
    // 为每个列创建单元格（不包括操作列）
    for (let i = 0; i < headerCells.length - 1; i++) {
        const td = document.createElement('td');
        td.contentEditable = true;
        td.dataset.col = i;
        td.dataset.rowNum = newRowNum;
        
        // 为数字列添加numeric类
        if (i >= 3 && i <= 5) { // 成本、差价、补差列
            td.className = 'numeric';
        }
        
        newRow.appendChild(td);
    }
    
    // 添加操作按钮单元格
    const actionCell = document.createElement('td');
    actionCell.innerHTML = `
        <button onclick="deleteTableRow(this)" class="action-btn" title="删除行">
            <i class="fas fa-trash-alt"></i>
        </button>
    `;
    newRow.appendChild(actionCell);
    
    // 在"添加行"按钮行之前插入新行
    tbody.insertBefore(newRow, lastRow);
    
    // 自动聚焦第一个单元格
    newRow.querySelector('td[contenteditable="true"]').focus();
    
    showToast('已添加新行', 'success');
}

// 获取编辑后的表格数据
function getEditedTableData() {
    const table = document.querySelector('.editable-table');
    if (!table) return '';
    
    const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent);
    // 排除最后一个表头（操作列）
    headers.pop();
    
    const rows = Array.from(table.querySelectorAll('tbody tr')).filter(tr => !tr.querySelector('button[title="添加行"]')); // 排除添加按钮行
    
    // 构建表格数据
    let tableData = '';
    
    // 添加表头行（如果需要）
    // tableData += headers.join('\t') + '\n';
    
    // 添加数据行
    rows.forEach(row => {
        // 排除操作列单元格
        const cells = Array.from(row.querySelectorAll('td[contenteditable="true"]')).map(td => td.textContent.trim().replace(/\n/g, ' ')); // 将单元格内的换行符替换为空格
        tableData += cells.join('\t') + '\n';
    });
    
    return tableData.trim();
}

// 转换粘贴的表格数据
function convertPastedText(text) {
    if (!text || typeof text !== 'string' || text.trim() === '') {
        return '';
    }
    
    const lines = text.split('\n');
    let output = [];
    let processedCount = 0;
    let totalAmount = 0; // 添加总金额计算

    console.log(`准备处理 ${lines.length} 行数据...`);

    for (let i = 0; i < lines.length; i++) {
        const currentLine = lines[i].trim();
        if (!currentLine) continue;

        const columns = currentLine.split(/\t+/);

        if (columns.length > 5) {
            const supplement = columns[5].trim();
            
            if (supplement && /^\d+$/.test(supplement)) {
                const supplementAmount = parseInt(supplement, 10);
                if (supplementAmount > 0) {
                    
                    let nextModelLineIndex = -1;
                    for (let j = i + 1; j < lines.length; j++) {
                        if (lines[j].trim()) {
                            nextModelLineIndex = j;
                            break;
                        }
                    }

                    if (nextModelLineIndex !== -1) {
                        const nextLineCols = lines[nextModelLineIndex].trim().split(/\t+/);
                        if (nextLineCols.length > 0) {
                            const productModel = nextLineCols[0].trim();
                            if (productModel) {
                                const resultLine = `+${supplementAmount} 更换 ${productModel}`;
                                output.push(resultLine);
                                totalAmount += supplementAmount; // 累加到总金额
                                processedCount++;
                                console.log(`配对: 第 ${i + 1} 行补差值(${supplementAmount}) + 第 ${nextModelLineIndex + 1} 行型号(${productModel})`);
                            }
                        }
                    }
                }
            }
        }
    }

    if (processedCount > 0) {
        console.log(`共转换了 ${processedCount} 行数据，总金额: ${totalAmount}`);
        showToast(`已转换 ${processedCount} 条补差数据`, 'success');

        // 返回两个版本的数据
        const itemsOnly = output.join('\n'); // 只有条目，不包含合计
        const withTotal = totalAmount > 0 ? itemsOnly + '\n' + `合计: ${totalAmount}` : itemsOnly; // 包含合计

        return {
            items: itemsOnly,      // 用于输入框
            withTotal: withTotal,  // 用于复制到剪贴板
            totalAmount: totalAmount
        };
    } else {
        console.log('没有找到有效的补差数据');
        showToast('未找到有效的补差数据', 'error');
        return null;
    }
}

// 复制表格数据
function copyTableData() {
    const table = document.querySelector('.editable-table');
    if (!table) {
        showToast('没有找到表格数据', 'error');
        return;
    }

    const rows = Array.from(table.querySelectorAll('tbody tr')).filter(tr => !tr.querySelector('button[title="添加行"]')); // 排除添加按钮行

    if (rows.length === 0) {
        showToast('表格数据为空', 'error');
        return;
    }

    let copyText = '';
    let totalRevenue = 0;

    // 按照每两行为一组处理
    for (let i = 0; i < rows.length; i += 2) {
        const currentRow = rows[i];
        const nextRow = rows[i + 1];

        if (!currentRow) continue;

        // 获取当前行的数据
        const currentCells = Array.from(currentRow.querySelectorAll('td[contenteditable]'));
        if (currentCells.length < 7) continue; // 确保有足够的列

        const currentModel = currentCells[0].textContent.trim(); // 型号
        const currentRevenue = parseFloat(currentCells[6].textContent) || 0; // 创收

        // 如果有下一行，获取下一行的型号
        if (nextRow) {
            const nextCells = Array.from(nextRow.querySelectorAll('td[contenteditable]'));
            if (nextCells.length >= 1) {
                const nextModel = nextCells[0].textContent.trim(); // 替换型号

                // 格式：替换型号 +创收金额
                if (nextModel && currentRevenue !== 0) {
                    copyText += `${nextModel} +${currentRevenue}\n`;
                    totalRevenue += currentRevenue;
                }
            }
        } else {
            // 如果没有下一行，只输出当前型号和创收
            if (currentModel && currentRevenue !== 0) {
                copyText += `${currentModel} +${currentRevenue}\n`;
                totalRevenue += currentRevenue;
            }
        }
    }

    // 添加合计
    if (totalRevenue !== 0) {
        copyText += `合计：${totalRevenue}`;
    }

    if (!copyText.trim()) {
        showToast('没有有效的数据可复制', 'error');
        return;
    }

    // 复制到剪贴板
    const tempTextarea = document.createElement('textarea');
    tempTextarea.value = copyText;
    document.body.appendChild(tempTextarea);
    tempTextarea.select();
    document.execCommand('copy');
    document.body.removeChild(tempTextarea);

    showToast('数据已复制到剪贴板', 'success');
}

// 显示删除确认弹窗
function showDeleteModal(recordId) {
    document.getElementById('deleteConfirmText').textContent = `您确定要删除记录 #${recordId} 吗？此操作不可撤销。`;
    document.getElementById('confirmDeleteButton').onclick = function() {
        confirmDeleteRecord(recordId);
    };
    document.getElementById('deleteModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// 关闭删除确认弹窗
function closeDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// 确认删除记录
async function confirmDeleteRecord(recordId) {
    closeDeleteModal();
    
    // 显示删除中提示
    showToast('删除中...', 'success');
    
    try {
        const response = await sendAuthenticatedRequest(`/api/price-records/${recordId}`, {
            method: 'DELETE'
        });
        
        if (!response) {
            showToast('身份验证失败', 'error');
            return;
        }
        
        if (response.ok) {
            showToast('记录已删除', 'success');
            searchRecords(); // 刷新列表
        } else {
            const errorData = await response.json();
            throw new Error(errorData.message || '删除失败');
        }
    } catch (error) {
        showToast('删除失败: ' + error.message, 'error');
        console.error('Error:', error);
    }
}

// 删除记录 - 显示确认弹窗
function deleteRecord(recordId) {
    // 检查管理员权限
    if (!isCurrentUserAdmin()) {
        showToast('权限不足，只有管理员可以删除记录', 'error');
        return;
    }
    showDeleteModal(recordId);
}

// 打开原始表格数据模态框
function openRawTableModal() {
    const rawTableModal = document.getElementById('rawTableModal');
    rawTableModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // 修改为从localStorage加载数据
    loadRawTableList();
}

// 关闭原始表格数据模态框
function closeRawTableModal() {
    document.getElementById('rawTableModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// 切换原始表格数据页码
function changeRawTablePage(delta) {
    rawTableCurrentPage += delta;
    if (rawTableCurrentPage < 1) rawTableCurrentPage = 1;
    if (rawTableCurrentPage > rawTableTotalPages) rawTableCurrentPage = rawTableTotalPages;
    loadRawTableList();
}

// 加载原始表格数据列表
async function loadRawTableList() {
    const rawTableList = document.getElementById('rawTableList');
    rawTableList.innerHTML = `<tr><td colspan="${getRawTableColspan()}" style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> 加载中...</td></tr>`;
    
    // 构建查询URL
    const url = `/api/raw-table-data?page=${rawTableCurrentPage}&limit=${rawTablePageSize}&search=${encodeURIComponent(rawTableSearchQuery)}&category=${rawTableSearchCategory}`;
    
    try {
        const response = await sendAuthenticatedRequest(url);
        if (!response) return;

        const data = await response.json();
        const records = data.records || [];
        rawTableTotalPages = Math.ceil(data.total / rawTablePageSize);
        
        // 更新分页信息
        document.getElementById('rawTableRecordCount').textContent = `总记录: ${data.total}`;
        document.getElementById('rawTableCurrentPage').textContent = `第${rawTableCurrentPage}页 / 共${rawTableTotalPages}页`;
        document.getElementById('rawTablePrevPage').disabled = rawTableCurrentPage <= 1;
        document.getElementById('rawTableNextPage').disabled = rawTableCurrentPage >= rawTableTotalPages;
        
        rawTableList.innerHTML = '';

        if (records.length === 0) {
            rawTableList.innerHTML = `<tr><td colspan="${getRawTableColspan()}" style="text-align: center; padding: 20px;">暂无记录</td></tr>`;
            return;
        }
        
        const fragment = document.createDocumentFragment();
        
        records.forEach((record, index) => {
            // 尝试提取表格的第一行作为表头和第二行作为数据预览
            let previewText = '无数据预览';
            if (record.raw_data) {
                const lines = record.raw_data.trim().split('\n');
        if (lines.length >= 2) {
                    const header = lines[0].split('\t')[0] || '型号';
                    const firstDataRow = lines[1].split('\t')[0] || '';
                    previewText = `<strong>${header}:</strong> ${firstDataRow}`;
                    if (lines.length > 2) {
                        previewText += ` (共${lines.length-1}个项目)`;
                    }
                }
            }
            
            const date = formatDate(record.created_at);
            
            const tr = document.createElement('tr');
            tr.style.animation = `slideInUp 0.4s ${index * 0.05}s ease-out backwards`;

            // 根据用户角色决定是否显示删除按钮
            const deleteButton = isCurrentUserAdmin() ?
                `<button onclick="confirmDeleteRawTableData(${record.id})" class="action-btn" title="删除" style="background-color: #ff4757;">
                    <i class="fas fa-trash-alt"></i>
                </button>` : '';

            // 根据用户角色决定是否显示创建者列
            const creatorCell = isCurrentUserAdmin() ?
                `<td style="text-align: center;">
                    <span style="background: var(--clay-container-bg); padding: 2px 8px; border-radius: 12px; font-size: 12px;">
                        ${record.created_by || '未知用户'}
                    </span>
                </td>` : '';

            tr.innerHTML = `
                <td>${record.id}</td>
                <td>${date}</td>
                <td style="text-align: right;">¥${record.total_amount}</td>
                <td>${previewText}</td>
                ${creatorCell}
                <td style="text-align: center;">
                    <div style="display: flex; justify-content: center; gap: 5px;">
                        <button onclick="viewRawTableDetail(${record.id})" class="action-btn" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="applyRawTableDataDirectly(${record.id})" class="action-btn" title="应用">
                            <i class="fas fa-check"></i>
                        </button>
                        ${deleteButton}
                    </div>
                </td>`;
            fragment.appendChild(tr);
        });
        
        rawTableList.appendChild(fragment);
        
    } catch (error) {
            console.error('加载数据出错:', error);
            
            rawTableList.innerHTML = `
                <tr>
                    <td colspan="${getRawTableColspan()}" style="text-align: center; padding: 20px;">
                        加载数据出错，请刷新重试
                    </td>
                </tr>
            `;
        
    }
}

// 应用原始表格数据（直接从列表）
async function applyRawTableDataDirectly(recordId) {
    try {
        const response = await sendAuthenticatedRequest(`/api/raw-table-data/${recordId}`);
        if (!response) return;

        const data = await response.json();
        if (!data.record) {
            showToast('找不到记录', 'error');
            return;
        }
        
        // 将数据填充到表格转换输入框
        document.getElementById('rawInput').value = data.record.raw_data;
        
        // 关闭模态框
        closeRawTableModal();
        
        // 滚动到转换区域
        const rawInputElement = document.getElementById('rawInput');
        if (rawInputElement) {
            // 获取最近的父级form-section
            let parent = rawInputElement.parentElement;
            while (parent && !parent.classList.contains('form-section')) {
                parent = parent.parentElement;
            }
            if (parent) {
                parent.scrollIntoView({ behavior: 'smooth' });
            }
        }
        
        showToast('已加载原始表格数据，请点击转换按钮进行转换', 'success');
    } catch(error) {
        showToast('加载数据失败: ' + error.message, 'error');
        console.error('Error:', error);
    }
}

// 显示删除确认对话框
function confirmDeleteRawTableData(recordId) {
    // 检查管理员权限
    if (!isCurrentUserAdmin()) {
        showToast('权限不足，只有管理员可以删除记录', 'error');
        return;
    }

    if (confirm(`确定要删除ID为 ${recordId} 的原始表格数据吗？此操作不可撤销。`)) {
        deleteRawTableData(recordId);
    }
}

// 删除原始表格数据
async function deleteRawTableData(recordId) {
    showToast('删除中...', 'success');
    
    try {
        const response = await sendAuthenticatedRequest(`/api/raw-table-data/${recordId}`, {
            method: 'DELETE'
        });

        if (!response) {
             showToast('身份验证失败', 'error');
             return;
        }
    
        if (response.ok) {
            showToast('删除成功', 'success');
            loadRawTableList(); // 重新加载列表
        } else {
            const data = await response.json();
            throw new Error(data.message || '删除失败');
        }
    } catch(error) {
        showToast('删除失败: ' + error.message, 'error');
        console.error('Error:', error);
    }
}

// 查看原始表格详情
async function viewRawTableDetail(recordId) {
    currentRawTableId = recordId;
    
    const detailModal = document.getElementById('rawTableDetailModal');
    const rawTableDetail = document.getElementById('rawTableDetail');
    rawTableDetail.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>';
    
    try {
        const response = await sendAuthenticatedRequest(`/api/raw-table-data/${recordId}`);
        if (!response) return;

        const data = await response.json();

        if (!data.record) {
            rawTableDetail.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--error-color);">记录不存在</div>';
            return;
        }
        
        const record = data.record;
        currentRawTableData = record.raw_data;
        
        const date = formatDate(record.created_at);
        // 使用固定表头
        const fixedHeaders = ['型号', '库存状态', '数量', '成本', '差价', '补差', '创收'];
        const tableContent = parseRawTableData(record.raw_data, fixedHeaders);
        
        let html = `
            <div style="margin-bottom: 15px; display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center;">
                <span style="margin-bottom: 5px;">创建时间: ${date}</span>
                <span style="font-size: 18px; font-weight: bold; color: var(--button-color); margin-bottom: 5px;">总金额: ¥${record.total_amount}</span>
            </div>
            <div style="background-color: var(--input-bg-color); padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; margin-bottom: 10px;">表格数据预览</h4>
                <div class="table-container" style="overflow-x: auto;">
                    ${tableContent}
                </div>
            </div>
            <div style="background-color: var(--input-bg-color); padding: 15px; border-radius: 8px;">
                <h4 style="margin-top: 0; margin-bottom: 10px;">原始数据</h4>
                <pre style="background-color: var(--card-bg-color); padding: 10px; border-radius: 8px; overflow-x: auto; white-space: pre-wrap; word-break: break-all; max-height: 200px; overflow-y: auto;">${record.raw_data}</pre>
            </div>`;
        
        rawTableDetail.innerHTML = html;
        document.getElementById('rawTableDetailTitle').textContent = `表格数据详情 (ID: ${recordId})`;
        
        detailModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    } catch(error) {
        rawTableDetail.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--error-color);">加载失败</div>';
        console.error('Error:', error);
    }
}

// 关闭原始表格详情模态框
function closeRawTableDetailModal() {
    document.getElementById('rawTableDetailModal').style.display = 'none';
    // 恢复背景滚动
    document.body.style.overflow = 'auto';
    // 显示原始表格列表模态框
    document.getElementById('rawTableModal').style.display = 'block';
}

// 应用原始表格数据
function applyRawTableData() {
    if (currentRawTableData) {
        // 将数据填充到表格转换输入框
        document.getElementById('rawInput').value = currentRawTableData;
        
        // 关闭模态框
        closeRawTableDetailModal();
        closeRawTableModal();
        
        // 滚动到转换区域
        const rawInputElement = document.getElementById('rawInput');
        if (rawInputElement) {
            // 获取最近的父级form-section
            let parent = rawInputElement.parentElement;
            while (parent && !parent.classList.contains('form-section')) {
                parent = parent.parentElement;
            }
            if (parent) {
                parent.scrollIntoView({ behavior: 'smooth' });
            }
        }
        
        // 自动显示表格
        processTableData();
        
        showToast('已加载原始表格数据', 'success');
    } else {
        showToast('没有可应用的数据', 'error');
    }
}

// 解析表格数据为HTML表格
function parseRawTableData(rawData, fixedHeaders = null) {
    if (!rawData || typeof rawData !== 'string' || rawData.trim() === '') {
        return '<p>无有效数据</p>';
    }
    
    const lines = rawData.trim().split('\n');
    if (lines.length === 0) {
        return '<p>无有效数据行</p>';
    }
    
    let tableHtml = '<table class="detail-table">';
    
    // 处理表头 - 使用固定表头或从数据中提取
    let headers = [];
    if (fixedHeaders) {
        headers = fixedHeaders;
    } else {
        headers = lines[0].split('\t');
    }
    
    tableHtml += '<thead><tr>';
    for (let i = 0; i < headers.length; i++) {
        const header = headers[i] ? headers[i].trim() : `列${i+1}`;
        tableHtml += `<th>${header}</th>`;
    }
    tableHtml += '</tr></thead>';
    
    // 处理表格内容
    tableHtml += '<tbody>';
    const startLine = fixedHeaders ? 0 : 1; // 如果使用固定表头，则从第0行开始处理数据
    for (let i = startLine; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line === '') continue;
        
        // 确保每一行都能被处理，即使列数与表头不一致
        const cells = line.split('\t');
        tableHtml += '<tr>';
        
        // 动态填充单元格，确保单元格数量与表头一致
        for (let j = 0; j < headers.length; j++) {
            let cellValue = '';

            // 如果是创收列（最后一列），计算创收值
            if (fixedHeaders && j === headers.length - 1 && headers[j] === '创收') {
                // 创收 = 补差 - 差价
                const buchaStr = cells[5] ? cells[5].trim() : '';
                const chajiaStr = cells[4] ? cells[4].trim() : '';
                const bucha = buchaStr !== '' ? parseFloat(buchaStr) : 0;
                const chajia = chajiaStr !== '' ? parseFloat(chajiaStr) : 0;

                // 只有当补差或差价不为空时才计算创收
                if (buchaStr !== '' || chajiaStr !== '') {
                    cellValue = bucha - chajia;
                } else {
                    cellValue = '';
                }
            } else {
                // 其他列使用原始数据
                cellValue = j < cells.length ? cells[j] : '';
            }

            // 判断是否是数字，如果是则右对齐
            const isNumeric = !isNaN(parseFloat(cellValue)) && isFinite(cellValue);
            const cellClass = isNumeric ? 'numeric' : '';

            tableHtml += `<td class="${cellClass}">${cellValue}</td>`;
        }
        
        tableHtml += '</tr>';
    }
    
    tableHtml += '</tbody></table>';
    
    return tableHtml;
}

// 搜索原始表格数据
function searchRawTables() {
    rawTableSearchQuery = document.getElementById('rawTableSearchInput').value;
    rawTableSearchCategory = document.getElementById('rawTableSearchType').value;
    rawTableCurrentPage = 1;
    loadRawTableList();
}

// 重置原始表格数据搜索
function resetRawTableSearch() {
    document.getElementById('rawTableSearchInput').value = '';
    document.getElementById('rawTableSearchType').value = 'all';
    rawTableSearchQuery = '';
    rawTableSearchCategory = 'all';
    rawTableCurrentPage = 1;
    loadRawTableList();
    showToast('已重置搜索', 'success');
}

// 新增：动画辅助函数
const animateCSS = (elementOrSelector, animation, prefix = 'animate__') =>
    new Promise((resolve, reject) => {
        const animationName = `${prefix}${animation}`;
        const element = typeof elementOrSelector === 'string' ? document.querySelector(elementOrSelector) : elementOrSelector;

        if (!element) {
            // 如果元素不存在，直接返回一个被拒绝的Promise
            console.warn(`Animation target ${elementOrSelector} not found`);
            return reject(`Element not found: ${elementOrSelector}`);
        }

        element.classList.add(`${prefix}animated`, animationName);

        function handleAnimationEnd(event) {
            event.stopPropagation();
            element.classList.remove(`${prefix}animated`, animationName);
            resolve('Animation ended');
        }

        element.addEventListener('animationend', handleAnimationEnd, {
            once: true
        });
    });

// 页面加载动画增强 - 简化版本
function addPageLoadAnimations() {
    try {
        // 为主要容器添加动画类
        const container = document.querySelector('.container');
        if (container && !container.classList.contains('page-transition')) {
            container.classList.add('page-transition');
        }

        // 为快捷按钮添加简单的淡入动画
        const quickButtons = document.querySelectorAll('.quick-button');
        quickButtons.forEach((button, index) => {
            if (!button.classList.contains('animate__animated')) {
                button.style.opacity = '0';
                button.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    button.style.transition = 'all 0.5s ease';
                    button.style.opacity = '1';
                    button.style.transform = 'translateY(0)';
                }, index * 50);
            }
        });
    } catch (error) {
        console.warn('页面加载动画失败:', error);
    }
}

// 增强UI交互动画
function enhanceUIInteractions() {
    // 为按钮添加波纹效果（不干扰原有点击事件）
    const buttons = document.querySelectorAll('button, .button, .action-btn, .quick-button');
    buttons.forEach(button => {
        // 添加点击波纹效果（使用mousedown事件避免干扰click事件）
        button.addEventListener('mousedown', function(e) {
            // 创建波纹元素
            const ripple = document.createElement('span');
            ripple.classList.add('ripple-effect');

            // 设置波纹位置
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = `${size}px`;
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;

            // 确保按钮有相对定位
            if (getComputedStyle(this).position === 'static') {
                this.style.position = 'relative';
            }

            // 添加波纹到按钮
            this.appendChild(ripple);

            // 动画结束后移除波纹
            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.parentNode.removeChild(ripple);
                }
            }, 600);
        });
    });

    // 为输入框添加焦点动画
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.classList.add('input-focus-animation');
        });

        input.addEventListener('blur', function() {
            this.classList.remove('input-focus-animation');
        });
    });
}

// 数据分析相关功能
let dailyAmountChart = null;
let timeDistributionChart = null;
let partTypeChart = null;
let operationTypeChart = null;
let dailyAOVChart = null;

// 打开数据分析模态框
function openDataAnalysisModal() {
    const dataAnalysisModal = document.getElementById('dataAnalysisModal');
    dataAnalysisModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // 先销毁已有的图表实例
    if (dailyAmountChart) {
        dailyAmountChart.destroy();
        dailyAmountChart = null;
    }
    
    if (timeDistributionChart) {
        timeDistributionChart.destroy();
        timeDistributionChart = null;
    }
    
    if (partTypeChart) {
        partTypeChart.destroy();
        partTypeChart = null;
    }
    if (operationTypeChart) {
        operationTypeChart.destroy();
        operationTypeChart = null;
    }
    if (dailyAOVChart) {
        dailyAOVChart.destroy();
        dailyAOVChart = null;
    }
    
    // 加载和初始化图表
    initializeCharts();
    
    // 加载数据
    loadAnalysisData();
}

// 关闭数据分析模态框
function closeDataAnalysisModal() {
    document.getElementById('dataAnalysisModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    
    // 关闭时也销毁图表实例，避免内存泄漏
    if (dailyAmountChart) {
        dailyAmountChart.destroy();
        dailyAmountChart = null;
    }
    
    if (timeDistributionChart) {
        timeDistributionChart.destroy();
        timeDistributionChart = null;
    }
    
    if (partTypeChart) {
        partTypeChart.destroy();
        partTypeChart = null;
    }
    if (operationTypeChart) {
        operationTypeChart.destroy();
        operationTypeChart = null;
    }
    if (dailyAOVChart) {
        dailyAOVChart.destroy();
        dailyAOVChart = null;
    }
}

// 初始化所有图表
function initializeCharts() {
    initializeDailyAmountChart();
    initializeTimeDistributionChart();
    initializePartTypeChart();
    initializeOperationTypeChart();
    initializeDailyAOVChart();
}

// 初始化每日金额曲线图
function initializeDailyAmountChart() {
    const container = document.getElementById('dailyAmountChartContainer');
    
    // 清空容器
    container.innerHTML = '';
    
    // 创建新的canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'dailyAmountChart-' + Date.now(); // 使用时间戳创建唯一ID
    container.appendChild(canvas);
    
    const ctx = canvas.getContext('2d');
    
    const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--clay-primary').trim();
    const bgColor = isDarkMode ? 'rgba(0, 209, 255, 0.1)' : 'rgba(143, 157, 125, 0.1)';
    
    dailyAmountChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [], // 日期标签将由updateDailyAmountChart填充
            datasets: [{
                label: '每日总金额',
                data: [],
                borderColor: primaryColor,
                backgroundColor: bgColor,
                fill: true,
                tension: 0.2,
                pointBackgroundColor: primaryColor,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `金额: ¥${context.raw}`;
                        }
                    }
                },
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim()
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim(),
                        maxRotation: 45,
                        minRotation: 45
                    },
                    grid: {
                        display: true,
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim(),
                        callback: function(value) {
                            return '¥' + value;
                        }
                    },
                    grid: {
                        display: true,
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    }
                }
            }
        }
    });
}

// 更新每日金额图
function updateDailyAmountChart(dailyAmounts) {
    if (!dailyAmountChart) return;
    handleChartNoData(dailyAmountChart, 'dailyAmountChartContainer', dailyAmounts);
    if (!dailyAmounts || dailyAmounts.length === 0) return;

    // 格式化日期为更易读的格式
    const formatDate = (dateStr) => {
        const date = new Date(dateStr);
        return `${date.getMonth()+1}月${date.getDate()}日`;
    };
    
    // 提取数据和标签
    const labels = dailyAmounts.map(item => formatDate(item.date));
    const data = dailyAmounts.map(item => parseInt(item.total_amount));
    
    // 更新图表数据
    dailyAmountChart.data.labels = labels;
    dailyAmountChart.data.datasets[0].data = data;
    dailyAmountChart.update();
}

// 初始化时间段分布图
function initializeTimeDistributionChart() {
    const container = document.getElementById('timeDistributionChartContainer');
    
    // 清空容器
    container.innerHTML = '';
    
    // 创建新的canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'timeDistributionChart-' + Date.now(); // 使用时间戳创建唯一ID
    container.appendChild(canvas);
    
    const ctx = canvas.getContext('2d');
    
    // 生成渐变色
    const colors = isDarkMode ? 
        ['#00D1FF', '#43B581', '#8A63D2', '#F04747', '#F5A623', '#4F9BFF'] : 
        ['#8F9D7D', '#A7B896', '#6A7F5B', '#98C379', '#56B6C2', '#61AFEF'];
    
    timeDistributionChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['凌晨 (0-6点)', '上午 (6-12点)', '下午 (12-18点)', '晚上 (18-24点)'],
            datasets: [{
                label: '订单数量',
                data: [0, 0, 0, 0],
                backgroundColor: colors,
                borderWidth: 1,
                borderRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.raw;
                            const percentage = context.dataset.percentages[context.dataIndex];
                            return `订单数: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim()
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim(),
                        precision: 0
                    },
                    grid: {
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    }
                }
            }
        }
    });
}

// 初始化配件类型饼图
function initializePartTypeChart() {
    const container = document.getElementById('partTypeChartContainer');
    
    // 清空容器
    container.innerHTML = '';
    
    // 创建新的canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'partTypeChart-' + Date.now(); // 使用时间戳创建唯一ID
    container.appendChild(canvas);
    
    const ctx = canvas.getContext('2d');
    
    // 生成渐变色
    const colors = isDarkMode ? 
        ['#00D1FF', '#43B581', '#8A63D2', '#F04747', '#F5A623', '#4F9BFF', '#E91E63', '#9C27B0'] : 
        ['#8F9D7D', '#A7B896', '#6A7F5B', '#98C379', '#56B6C2', '#61AFEF', '#C678DD', '#E06C75'];
    
    partTypeChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: colors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim(),
                        padding: 15,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.raw;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${context.label}: ${value}个 (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '50%'
        }
    });
}

// 初始化操作类型分布图
function initializeOperationTypeChart() {
    const container = document.getElementById('operationTypeChartContainer');
    container.innerHTML = '';
    const canvas = document.createElement('canvas');
    canvas.id = 'operationTypeChart-' + Date.now();
    container.appendChild(canvas);
    const ctx = canvas.getContext('2d');
    
    const colors = isDarkMode ? 
        ['#8A63D2', '#00D1FF', '#43B581', '#F04747'] : 
        ['#6A7F5B', '#8F9D7D', '#A7B896', '#E06C75'];

    operationTypeChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: [], // e.g., ['加装', '更换']
            datasets: [{
                data: [],
                backgroundColor: colors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim()
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.raw;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                            return `${context.label}: ${value}次 (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// 初始化每日平均订单金额图
function initializeDailyAOVChart() {
    const container = document.getElementById('dailyAOVChartContainer');
    container.innerHTML = '';
    const canvas = document.createElement('canvas');
    canvas.id = 'dailyAOVChart-' + Date.now();
    container.appendChild(canvas);
    const ctx = canvas.getContext('2d');
    
    const primaryColor = '#43B581'; // 使用绿色
    const bgColor = isDarkMode ? 'rgba(67, 181, 129, 0.1)' : 'rgba(67, 181, 129, 0.1)';
    
    dailyAOVChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '每日平均订单金额',
                data: [],
                borderColor: primaryColor,
                backgroundColor: bgColor,
                fill: true,
                tension: 0.2,
                pointBackgroundColor: primaryColor,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `平均金额: ¥${Number(context.raw).toFixed(2)}`;
                        }
                    }
                },
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim()
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim(),
                        maxRotation: 45,
                        minRotation: 45
                    },
                    grid: {
                        display: true,
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim(),
                        callback: function(value) {
                            return '¥' + value;
                        }
                    },
                    grid: {
                        display: true,
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    }
                }
            }
        }
    });
}

// 加载分析数据
function loadAnalysisData() {
    const timeRange = document.getElementById('timeRange').value;
    showToast('数据加载中...', 'success');
    
    // 从API获取数据
    sendAuthenticatedRequest(`/api/price-analytics?days=${timeRange}`)
        .then(response => {
            if (!response) return;
            return response.json();
        })
        .then(data => {
            if (data && data.success) {
                updateDailyAmountChart(data.dailyAmounts);
                updateTimeDistributionChart(data.timeDistribution);
                updatePartTypeChart(data.partTypes);
                updateOperationTypeChart(data.operationTypeDistribution);
                updateDailyAOVChart(data.dailyAOV);
                showToast('数据加载完成', 'success');
            } else if(data) {
                showToast('数据加载失败: ' + (data.message || '未知错误'), 'error');
            }
        })
        .catch(error => {
            console.error('加载分析数据出错:', error);
            showToast('数据加载失败: ' + error.message, 'error');
        });
}

// 更新时间段分布图
function updateTimeDistributionChart(timeDistribution) {
    if (!timeDistributionChart) return;

    const hasData = timeDistribution && Object.values(timeDistribution).some(v => v > 0);
    handleChartNoData(timeDistributionChart, 'timeDistributionChartContainer', hasData ? [1] : []); // Use dummy data to show/hide
    if (!hasData) return;

    const data = [
        timeDistribution.early_morning || 0,
        timeDistribution.morning || 0,
        timeDistribution.afternoon || 0,
        timeDistribution.evening || 0
    ];
    
    const total = data.reduce((acc, val) => acc + val, 0);
    const percentages = data.map(val => total > 0 ? ((val / total) * 100).toFixed(1) : 0);
    
    // 更新图表数据
    timeDistributionChart.data.datasets[0].data = data;
    timeDistributionChart.data.datasets[0].percentages = percentages;
    timeDistributionChart.update();
}

// 更新配件类型图
function updatePartTypeChart(partTypes) {
    if (!partTypeChart) return;
    handleChartNoData(partTypeChart, 'partTypeChartContainer', partTypes);
    if (!partTypes || partTypes.length === 0) return;
    
    // 提取标签和数据
    const labels = partTypes.map(item => item.description);
    const data = partTypes.map(item => item.count);
    
    // 更新图表数据
    partTypeChart.data.labels = labels;
    partTypeChart.data.datasets[0].data = data;
    partTypeChart.update();
}

// 更新操作类型分布图
function updateOperationTypeChart(operationTypes) {
    if (!operationTypeChart) return;
    handleChartNoData(operationTypeChart, 'operationTypeChartContainer', operationTypes);
    if (!operationTypes || operationTypes.length === 0) return;
    
    const labels = operationTypes.map(item => item.operation_type);
    const data = operationTypes.map(item => item.count);
    
    operationTypeChart.data.labels = labels;
    operationTypeChart.data.datasets[0].data = data;
    operationTypeChart.update();
}

// 更新每日平均订单金额图
function updateDailyAOVChart(dailyAOV) {
    if (!dailyAOVChart) return;
    handleChartNoData(dailyAOVChart, 'dailyAOVChartContainer', dailyAOV);
    if (!dailyAOV || dailyAOV.length === 0) return;
    
    const formatDate = (dateStr) => {
        const date = new Date(dateStr);
        return `${date.getMonth()+1}月${date.getDate()}日`;
    };
    
    const labels = dailyAOV.map(item => formatDate(item.date));
    const data = dailyAOV.map(item => parseFloat(item.average_amount));
    
    dailyAOVChart.data.labels = labels;
    dailyAOVChart.data.datasets[0].data = data;
    dailyAOVChart.update();
}

// 更新所有分析图表
function updateAnalysisCharts() {
    loadAnalysisData();
}

// 手动刷新分析数据
function refreshAnalysisData() {
    // 重置并重新加载数据
    if (dailyAmountChart) {
        dailyAmountChart.data.datasets[0].data = [];
        dailyAmountChart.update();
    }
    
    if (timeDistributionChart) {
        timeDistributionChart.data.datasets[0].data = [0, 0, 0, 0];
        timeDistributionChart.update();
    }
    
    if (partTypeChart) {
        partTypeChart.data.labels = [];
        partTypeChart.data.datasets[0].data = [];
        partTypeChart.update();
    }

    if (operationTypeChart) {
        operationTypeChart.data.labels = [];
        operationTypeChart.data.datasets[0].data = [];
        operationTypeChart.update();
    }
    if (dailyAOVChart) {
        dailyAOVChart.data.datasets[0].data = [];
        dailyAOVChart.update();
    }
    
    loadAnalysisData();
}

// 邀请定制计算器功能
function calculateCustomTotal(withAnimation = false) {
    const machinePrice = parseInt(document.getElementById('machinePrice').value) || 0;
    const extraPrice = parseInt(document.getElementById('extraPrice').value) || 0;
    const totalPrice = machinePrice + extraPrice;
    
    document.getElementById('customTotalPrice').textContent = totalPrice;
    
    // 只在明确要求时添加动画效果，避免输入时频繁动画
    if (withAnimation) {
        animateCSS('#customTotalPrice', 'heartBeat');
    }
    
    return totalPrice;
}

// 复制邀请定制总价
function copyCustomTotal() {
    const machinePrice = parseInt(document.getElementById('machinePrice').value) || 0;
    const extraPrice = parseInt(document.getElementById('extraPrice').value) || 0;
    
    if (machinePrice <= 0 && extraPrice <= 0) {
        showToast('请输入有效价格', 'error');
        return;
    }
    
    const totalPrice = calculateCustomTotal(false);
    
    // 复制总价到剪贴板
    const tempInput = document.createElement('textarea');
    tempInput.value = totalPrice;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand('copy');
    document.body.removeChild(tempInput);
    
    // 提示复制成功
    showToast(`总价 ${totalPrice} 元已复制到剪贴板`, 'success');
    
    // 高亮显示总价
    animateCSS('#customTotalPrice', 'pulse');
    
    // 清空输入框
    document.getElementById('machinePrice').value = '';
    document.getElementById('extraPrice').value = '';
    document.getElementById('customTotalPrice').textContent = '0';
    
    // 提示已清空
    setTimeout(() => {
        showToast('已清空输入框', 'success');
    }, 1000);
}

// 为邀请定制输入框添加回车键处理
document.addEventListener('DOMContentLoaded', function() {
    const previousListeners = document.querySelectorAll('#machinePrice, #extraPrice');

    previousListeners.forEach(input => {
        input.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                calculateCustomTotal();
            }
        });
    });

    // 为风扇和集线器输入框添加回车键处理
    const profitInputs = document.querySelectorAll('#fanQuantity, #hubQuantity');
    profitInputs.forEach(input => {
        input.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                calculateFanProfit();
                calculateHubProfit();
            }
        });
    });
});

// 风扇利润计算
function calculateFanProfit() {
    const quantity = parseInt(document.getElementById('fanQuantity').value) || 0;
    const pricePerFan = parseInt(document.getElementById('fanPrice').value) || 30;
    const costPerFan = 12; // 风扇成本固定12元

    const totalPrice = quantity * pricePerFan;
    const totalCost = quantity * costPerFan;
    const profit = totalPrice - totalCost;

    document.getElementById('fanTotalPrice').textContent = totalPrice;
    document.getElementById('fanCost').textContent = totalCost;
    document.getElementById('fanProfit').textContent = profit;

    // 更新总利润
    updateTotalProfit();
}

// 集线器利润计算
function calculateHubProfit() {
    const quantity = parseInt(document.getElementById('hubQuantity').value) || 0;
    const sellPrice = parseInt(document.getElementById('hubSellPrice').value) || 50;
    const costPerHub = 30; // 集线器成本固定30元

    const totalPrice = quantity * sellPrice;
    const totalCost = quantity * costPerHub;
    const profit = totalPrice - totalCost;

    document.getElementById('hubTotalPrice').textContent = totalPrice;
    document.getElementById('hubCost').textContent = totalCost;
    document.getElementById('hubProfit').textContent = profit;

    // 更新总利润
    updateTotalProfit();
}

// 更新总利润
function updateTotalProfit() {
    const fanProfit = parseInt(document.getElementById('fanProfit').textContent) || 0;
    const hubProfit = parseInt(document.getElementById('hubProfit').textContent) || 0;
    const totalProfit = fanProfit + hubProfit;

    document.getElementById('totalProfit').textContent = totalProfit;
}

// 重置利润计算器
function resetProfitCalculator() {
    // 重置风扇相关
    document.getElementById('fanQuantity').value = '';
    document.getElementById('fanPrice').value = '30';
    document.getElementById('fanTotalPrice').textContent = '0';
    document.getElementById('fanCost').textContent = '0';
    document.getElementById('fanProfit').textContent = '0';

    // 重置集线器相关
    document.getElementById('hubQuantity').value = '';
    document.getElementById('hubSellPrice').value = '50';
    document.getElementById('hubTotalPrice').textContent = '0';
    document.getElementById('hubCost').textContent = '0';
    document.getElementById('hubProfit').textContent = '0';

    // 重置总利润
    document.getElementById('totalProfit').textContent = '0';

    showToast('已重置利润计算器', 'success');
}

// 复制利润计算结果
function copyProfitResult() {
    const fanQuantity = parseInt(document.getElementById('fanQuantity').value) || 0;
    const fanProfit = parseInt(document.getElementById('fanProfit').textContent) || 0;

    const hubQuantity = parseInt(document.getElementById('hubQuantity').value) || 0;
    const hubProfit = parseInt(document.getElementById('hubProfit').textContent) || 0;

    let resultText = '';

    if (fanQuantity > 0 && fanProfit > 0) {
        resultText += `${fanQuantity}把风扇 +${fanProfit}\n`;
    }

    if (hubQuantity > 0 && hubProfit > 0) {
        resultText += `集线器 +${hubProfit}\n`;
    }

    if (fanQuantity === 0 && hubQuantity === 0) {
        showToast('请先输入风扇或集线器数据', 'error');
        return;
    }

    if (!resultText.trim()) {
        showToast('没有利润数据可复制', 'error');
        return;
    }

    // 去掉最后的换行符
    resultText = resultText.trim();

    // 复制到剪贴板
    const tempInput = document.createElement('textarea');
    tempInput.value = resultText;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand('copy');
    document.body.removeChild(tempInput);

    showToast('利润计算结果已复制到剪贴板', 'success');

    // 复制后自动重置
    setTimeout(() => {
        resetProfitCalculator();
    }, 1000);
}

// --- 新增：处理图表无数据的辅助函数 ---
function handleChartNoData(chart, containerId, data) {
    const container = document.getElementById(containerId);
    if (!container) return;
    const canvas = container.querySelector('canvas');
    let noDataMessage = container.querySelector('.no-data-message');

    if (!data || data.length === 0) {
        if(canvas) canvas.style.display = 'none';
        if (!noDataMessage) {
            noDataMessage = document.createElement('div');
            noDataMessage.className = 'no-data-message';
            noDataMessage.textContent = '暂无数据';
            container.appendChild(noDataMessage);
        }
        noDataMessage.style.display = 'block';
        if (chart) {
            chart.data.labels = [];
            chart.data.datasets.forEach((dataset) => {
                dataset.data = [];
            });
            chart.update();
        }
    } else {
        if(canvas) canvas.style.display = 'block';
        if (noDataMessage) noDataMessage.style.display = 'none';
    }
}

// --- 新增：操作类型分布图 ---
function initializeOperationTypeChart() {
    const container = document.getElementById('operationTypeChartContainer');
    container.innerHTML = '';
    const canvas = document.createElement('canvas');
    canvas.id = 'operationTypeChart-' + Date.now();
    container.appendChild(canvas);
    const ctx = canvas.getContext('2d');
    
    const colors = isDarkMode ? 
        ['#8A63D2', '#00D1FF', '#43B581', '#F04747'] : 
        ['#6A7F5B', '#8F9D7D', '#A7B896', '#E06C75'];

    operationTypeChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: [], // e.g., ['加装', '更换']
            datasets: [{
                data: [],
                backgroundColor: colors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim()
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.raw;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                            return `${context.label}: ${value}次 (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// --- 新增：每日平均订单金额图 ---
function initializeDailyAOVChart() {
    const container = document.getElementById('dailyAOVChartContainer');
    container.innerHTML = '';
    const canvas = document.createElement('canvas');
    canvas.id = 'dailyAOVChart-' + Date.now();
    container.appendChild(canvas);
    const ctx = canvas.getContext('2d');
    
    const primaryColor = '#43B581'; // 使用绿色
    const bgColor = isDarkMode ? 'rgba(67, 181, 129, 0.1)' : 'rgba(67, 181, 129, 0.1)';
    
    dailyAOVChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '每日平均订单金额',
                data: [],
                borderColor: primaryColor,
                backgroundColor: bgColor,
                fill: true,
                tension: 0.2,
                pointBackgroundColor: primaryColor,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `平均金额: ¥${Number(context.raw).toFixed(2)}`;
                        }
                    }
                },
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim()
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim(),
                        maxRotation: 45,
                        minRotation: 45
                    },
                    grid: {
                        display: true,
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--clay-text').trim(),
                        callback: function(value) {
                            return '¥' + value;
                        }
                    },
                    grid: {
                        display: true,
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    }
                }
            }
        }
    });
}

// --- 新增：差价计算工具功能 ---

// 全局变量，用于存储补差方案的纯文本
let buchaResultText = '';

// 打开差价计算模态框
function openBuchaModal() {
    const buchaModal = document.getElementById('buchaModal');
    buchaModal.style.display = 'block';
    document.body.style.overflow = 'hidden';

    // 简化动画，避免干扰功能
    try {
        // 添加内容动画
        animateCSS('#buchaModal .modal-content', 'fadeInUp').catch(() => {
            // 如果动画失败，忽略错误
        });
    } catch (error) {
        // 忽略动画错误
    }

    // 重置选择状态为默认的50/100元链接
    toggleBuchaType('normal');

    // 如果输入框有值，则直接计算
    const amountInput = document.getElementById('buchaAmount');
    if (amountInput && amountInput.value && parseInt(amountInput.value) > 0) {
        calculateBucha();
    }
}

// 关闭差价计算模态框
function closeBuchaModal() {
    const buchaModal = document.getElementById('buchaModal');

    // 直接关闭，不使用复杂动画
    buchaModal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// 计算补差方案
function calculateBucha() {
    const amountInput = document.getElementById('buchaAmount');
    const resultDiv = document.getElementById('buchaResult');
    const resultContent = document.getElementById('buchaResultContent');
    const amount = parseInt(amountInput.value);

    if (isNaN(amount) || amount <= 0) {
        showToast('请输入有效的金额数字（大于0）', 'error');
        amountInput.focus();
        return;
    }

    let linkType, count, totalAmount, linkText, specialInstruction = '';
    const highlightStyle = "color: var(--clay-primary); font-weight: bold;";
    const errorStyle = "color: var(--color-error); font-weight: bold;";
    const MAX_100_LINKS = 5; // 100元链接最多拍5个

    // 根据选择的补差类型处理
    if (selectedBuchaType === 'large') {
        // 大额补差 - 使用10000元链接
        linkType = 10000;
        count = 1;
        totalAmount = 10000;
        linkText = "10000元";
        
        let resultHTML = `
            <p>客户需要补差 <span style="${highlightStyle}">${amount}元</span>。</p>
            <p>您选择了<span style="${highlightStyle}">10000元链接</span>补差方式：</p>
            <ol style="padding-left: 20px; line-height: 1.6;">
                <li>直接拍下<span style="${highlightStyle}">1个10000元</span>的补差链接，<span style="${errorStyle}">不要付款</span></li>
                <li>联系客服说明需要补差${amount}元</li>
                <li>客服将价格改为您需要的具体金额</li>
            </ol>
            <p>拍下后<span style="${errorStyle}">不要付款</span>，等待客服为您改价到<span style="${highlightStyle}">${amount}元</span>后再支付。</p>
            <p>注意：补差链接<span style="${errorStyle}">不可使用优惠券</span>，否则无法改价！</p>
        `;
        
        resultContent.innerHTML = resultHTML;
        resultDiv.style.display = 'block';
        animateCSS('#buchaResult', 'fadeIn');
        
        // 为复制准备的纯文本版本 - 10000元链接
        buchaResultText = `您好，您需要补的差价金额为 ${amount}元。\n\n`;
        buchaResultText += `操作步骤如下：\n`;
        buchaResultText += `1.请您先拍下 1 个 10000 元的补差链接（拍下即可，暂时不要付款）。\n`;
        buchaResultText += `2.拍下后，请及时联系我们的客服。\n`;
        buchaResultText += `3.客服收到信息后，会将链接价格修改为您实际需要补的 ${amount} 元。\n\n`;
        buchaResultText += `重要操作提示：\n`;
        buchaResultText += `1.拍下链接后，请勿直接付款。\n`;
        buchaResultText += `2.进入支付页面后，请暂停操作（不要进行刷脸、不要输入支付密码）。\n`;
        buchaResultText += `3.务必联系客服完成改价后，再返回支付页面完成最终的 ${amount} 元 支付。\n`;
        buchaResultText += `4.请注意：差价链接不支持使用优惠券。\n\n`;
        buchaResultText += `感谢您的配合！完成以上步骤，客服会尽快为您处理。`;
        
        return;
    } else if (selectedBuchaType === 'bucha200') {
        // 200元补差
        linkType = 200;
        count = Math.ceil(amount / 200);
        totalAmount = count * 200;
        linkText = "200元";
        
        let resultHTML = `
            <p>客户需要补差 <span style="${highlightStyle}">${amount}元</span>。</p>
            <p>您需要一起拍 <span style="${highlightStyle}">${count}个</span> <span style="${highlightStyle}">${linkText}</span> 的补差链接，总金额 ${totalAmount}元。</p>
            <p>拍下后<span style="${errorStyle}">不要付款</span>，等待客服为您改价到<span style="${highlightStyle}">${amount}元</span>后再支付。</p>
            <p>注意：补差链接<span style="${errorStyle}">不可使用优惠券</span>，否则无法改价！</p>
        `;
        
        resultContent.innerHTML = resultHTML;
        resultDiv.style.display = 'block';
        animateCSS('#buchaResult', 'fadeIn');
        
        // 为复制准备的纯文本版本 - 200元链接
        buchaResultText = `您好，您需要补的差价金额为 ${amount}元。\n\n`;
        buchaResultText += `操作步骤如下：\n`;
        buchaResultText += `1.请您先拍下 ${count} 个 ${linkText} 的补差链接，共计 ${totalAmount} 元（拍下即可，暂时不要付款）。\n`;
        buchaResultText += `2.拍下后，请及时联系我们的客服。\n`;
        buchaResultText += `3.客服收到信息后，会将链接价格修改为您实际需要补的 ${amount} 元。\n\n`;
        buchaResultText += `重要操作提示：\n`;
        buchaResultText += `1.拍下链接后，请勿直接付款。\n`;
        buchaResultText += `2.进入支付页面后，请暂停操作（不要进行刷脸、不要输入支付密码）。\n`;
        buchaResultText += `3.务必联系客服完成改价后，再返回支付页面完成最终的 ${amount} 元 支付。\n`;
        buchaResultText += `4.请注意：差价链接不支持使用优惠券。\n\n`;
        buchaResultText += `感谢您的配合！完成以上步骤，客服会尽快为您处理。`;
        
        return;
    }
    
    // 普通补差 - 使用50元或100元链接
    if (amount <= 50) {
        // 50元补差链接
        linkType = 50;
        count = 1;
        totalAmount = 50;
        linkText = "50元";
    } else {
        // 使用100元链接
        linkType = 100;
        count = Math.ceil(amount / 100);
        
        // 确保不超过最大链接数量
        if (count > MAX_100_LINKS) {
            count = MAX_100_LINKS;
            specialInstruction = `
                <p>由于您的补差金额需要超过${MAX_100_LINKS}个100元链接，建议：</p>
                <ol style="padding-left: 20px; line-height: 1.6;">
                    <li>先拍下<span style="${highlightStyle}">${MAX_100_LINKS}个100元</span>的补差链接(共500元)，<span style="${errorStyle}">不要付款</span></li>
                    <li>联系客服说明需要补差${amount}元</li>
                    <li>客服将为您处理剩余补差部分</li>
                </ol>
            `;
        }
        
        totalAmount = count * 100;
        linkText = "100元";
    }
    
    let resultHTML = `
        <p>客户需要补差 <span style="${highlightStyle}">${amount}元</span>。</p>
    `;
    
                    // 根据金额提供不同内容
        if (amount > 500) {
            // 超过500元的情况
            const remainAmount = amount - 500;
            const remainLinks = Math.ceil(remainAmount / 100);
            
            resultHTML += `
                <p>您需要补差<span style="${highlightStyle}">${amount}元</span>，由于超过500元，请按照以下步骤操作：</p>
                <ol style="padding-left: 20px; line-height: 1.6;">
                    <li>请先拍下<span style="${highlightStyle}">5个100元</span>的补差链接，共计500元，<span style="${highlightStyle}">直接付款</span>。</li>
                    <li>付款后，请及时联系我们的客服。</li>
                    <li>客服收到信息后，会继续给您发差价链接，您需要拍下<span style="${highlightStyle}">${remainLinks}个100元</span>的补差链接，<span style="${errorStyle}">不要付款</span>。</li>
                    <li>联系客服改价后再支付。</li>
                </ol>
                
                <p><span style="${highlightStyle}">重要提示</span>：第一批链接(5个)需要直接付款，第二批链接拍下后<span style="${errorStyle}">不要付款</span>，等待客服改价。</p>
                <p>注意：差价链接<span style="${errorStyle}">不可使用优惠券</span>，否则无法改价！</p>
            `;
        } else if (specialInstruction) {
            resultHTML += specialInstruction;
        } else {
            resultHTML += `
                <p>您需要一起拍 <span style="${highlightStyle}">${count}个</span> <span style="${highlightStyle}">${linkText}</span> 的补差链接，总金额 ${totalAmount}元。</p>
                <p>拍下后<span style="${errorStyle}">不要付款</span>，等待客服为您改价到<span style="${highlightStyle}">${amount}元</span>后再支付。</p>
                <p>注意：差价链接<span style="${errorStyle}">不可使用优惠券</span>，否则无法改价！</p>
            `;
        }

    resultContent.innerHTML = resultHTML;
    resultDiv.style.display = 'block';
    animateCSS('#buchaResult', 'fadeIn');

    // 为复制准备的纯文本版本
    buchaResultText = `您好，您需要补的差价金额为 ${amount}元。\n\n`;
    buchaResultText += `操作步骤如下：\n`;
    
    if (amount <= 50) {
        // 50元链接
        buchaResultText += `1.请您先拍下 1 个 50 元的补差链接（拍下即可，暂时不要付款）。\n`;
        buchaResultText += `2.拍下后，请及时联系我们的客服。\n`;
        buchaResultText += `3.客服收到信息后，会将链接价格修改为您实际需要补的 ${amount} 元。\n\n`;
    } else if (amount > 500) {
        // 超过500元的处理方式
        const remainAmount = amount - 500;
        const remainLinks = Math.ceil(remainAmount / 100);
        
        buchaResultText += `1.请您先拍下 5 个 100 元的补差链接，共计 500 元，直接付款。\n`;
        buchaResultText += `2.拍下后，请及时联系我们的客服。\n`;
        buchaResultText += `3.客服收到信息后，会继续给您发差价链接，您拍下${remainLinks}个100的补差链接不付款，联系客服改价后再支付 。\n\n`;
    } else if (count >= MAX_100_LINKS) {
        // 正好500元
        buchaResultText += `1.请您先拍下 ${MAX_100_LINKS} 个 100 元的补差链接，共计 500 元（拍下即可，暂时不要付款）。\n`;
        buchaResultText += `2.拍下后，请及时联系我们的客服。\n`;
        buchaResultText += `3.客服收到信息后，会将链接价格修改为您实际需要补的 ${amount} 元。\n\n`;
    } else {
        // 正常的100元链接处理
        buchaResultText += `1.请您先拍下 ${count} 个 ${linkText} 的补差链接，共计 ${totalAmount} 元（拍下即可，暂时不要付款）。\n`;
        buchaResultText += `2.拍下后，请及时联系我们的客服。\n`;
        buchaResultText += `3.客服收到信息后，会将链接价格修改为您实际需要补的 ${amount} 元。\n\n`;
    }
    buchaResultText += `重要操作提示：\n`;
    
    if (amount > 500) {
        // 超过500元的提示，第一批链接直接付款，第二批不付款
        buchaResultText += `1.前5个链接请直接付款。\n`;
        buchaResultText += `2.后续链接拍下后，请勿直接付款。\n`;
        buchaResultText += `3.进入支付页面后，请暂停操作（不要进行刷脸、不要输入支付密码）。\n`;
        buchaResultText += `4.务必联系客服完成改价后，再返回支付页面完成最终的支付。\n`;
        buchaResultText += `5.请注意：差价链接不支持使用优惠券。\n\n`;
    } else {
        // 其他情况的常规提示
        buchaResultText += `1.拍下链接后，请勿直接付款。\n`;
        buchaResultText += `2.进入支付页面后，请暂停操作（不要进行刷脸、不要输入支付密码）。\n`;
        buchaResultText += `3.务必联系客服完成改价后，再返回支付页面完成最终的 ${amount} 元 支付。\n`;
        buchaResultText += `4.请注意：差价链接不支持使用优惠券。\n\n`;
    }
    buchaResultText += `感谢您的配合！完成以上步骤，客服会尽快为您处理。`;
}

// 重置补差计算器
function resetBucha() {
    const amountInput = document.getElementById('buchaAmount');
    const resultDiv = document.getElementById('buchaResult');
    const copyBtn = document.getElementById('buchaCopyBtn');
    
    amountInput.value = '';
    resultDiv.style.display = 'none';
    amountInput.focus();
    copyBtn.innerHTML = '<i class="fas fa-copy"></i> 复制';
}

// 复制补差方案
function copyBucha() {
    const copyBtn = document.getElementById('buchaCopyBtn');
    if (!buchaResultText) {
        showToast('请先生成补差方案', 'error');
        return;
    }

    const tempInput = document.createElement('textarea');
    tempInput.value = buchaResultText;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand('copy');
    document.body.removeChild(tempInput);
    
    showToast('方案已复制到剪贴板！', 'success');
    copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';

    setTimeout(() => {
        resetBucha();
    }, 1000);
}

// 为新功能添加事件监听器
document.addEventListener('DOMContentLoaded', () => {
    const buchaAmountInput = document.getElementById('buchaAmount');
    if (buchaAmountInput) {
         buchaAmountInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                calculateBucha();
            } else if (this.value && parseInt(this.value) > 0) {
                // 输入金额后自动计算，但添加短暂延迟避免频繁计算
                clearTimeout(this.timer);
                this.timer = setTimeout(() => {
                    calculateBucha();
                }, 500);
            }
        });
    }
   
    const buchaCalculateBtn = document.getElementById('buchaCalculateBtn');
    if(buchaCalculateBtn) buchaCalculateBtn.addEventListener('click', calculateBucha);

    const buchaResetBtn = document.getElementById('buchaResetBtn');
    if(buchaResetBtn) buchaResetBtn.addEventListener('click', resetBucha);

    const buchaCopyBtn = document.getElementById('buchaCopyBtn');
    if(buchaCopyBtn) buchaCopyBtn.addEventListener('click', copyBucha);
});

// 处理表格数据
function processTableData() {
    try {
        const rawInput = document.getElementById('rawInput').value;
        if (!rawInput.trim()) {
            showToast('请输入要转换的内容', 'error');
            return;
        }

        // 显示表格编辑区域
        showTableEditor(rawInput);
    } catch (error) {
        showToast('处理失败: ' + error.message, 'error');
        console.error('处理错误:', error);
    }
}

// 显示表格编辑区域 - 增强动画版本
function showTableEditor(rawData) {
    const tableEditModal = document.getElementById('tableEditModal');
    const editableTableContainer = document.getElementById('editableTableContainer');

    // 使用固定表头
    const fixedHeaders = ['型号', '库存状态', '数量', '成本', '差价', '补差', '创收'];

    // 解析原始数据为可编辑表格
    const tableContent = createEditableTable(rawData, fixedHeaders);
    editableTableContainer.innerHTML = tableContent;

    // 显示模态框
    tableEditModal.style.display = 'block';
    document.body.style.overflow = 'hidden';

    // 添加显示类以触发CSS过渡动画
    setTimeout(() => {
        tableEditModal.classList.add('show');
    }, 10);

    // 添加表格排序功能
    setupTableSorting();

    // 添加单元格验证
    setupCellValidation();

    // 动画效果
    animateCSS('#editableTableContainer', 'fadeIn');

    // 为表格行添加渐入动画
    animateTableRows();
}

// 为表格行添加动画效果
function animateTableRows() {
    const rows = document.querySelectorAll('.editable-table tbody tr');

    rows.forEach((row, index) => {
        // 移除任何现有的动画类
        row.classList.remove('animate__animated', 'animate__fadeInUp');

        // 添加新的动画类
        row.classList.add('animate__animated', 'animate__fadeInUp');

        // 设置延迟，使行依次出现
        row.style.animationDelay = `${index * 0.05}s`;

        // 添加鼠标悬停效果
        row.addEventListener('mouseenter', () => {
            row.style.transform = 'translateY(-2px)';
            row.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
            row.style.transition = 'all 0.3s var(--smooth-transition)';
            row.style.zIndex = '1';
        });

        row.addEventListener('mouseleave', () => {
            row.style.transform = 'translateY(0)';
            row.style.boxShadow = 'none';
            row.style.zIndex = 'auto';
        });
    });
}

// 隐藏表格编辑区域
function hideTableEditor() {
    const tableEditModal = document.getElementById('tableEditModal');

    // 直接关闭，不使用复杂动画
    tableEditModal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// 搜索表格
function searchTable() {
    const searchInput = document.getElementById('tableSearchInput');
    const searchText = searchInput.value.toLowerCase().trim();
    
    if (!searchText) {
        // 如果搜索框为空，显示所有行
        const rows = document.querySelectorAll('.editable-table tbody tr');
        rows.forEach(row => {
            if (!row.querySelector('button[title="添加行"]')) {
                row.style.display = '';
            }
        });
        return;
    }
    
    // 获取所有表格行
    const rows = document.querySelectorAll('.editable-table tbody tr');
    
    // 遍历每一行
    rows.forEach(row => {
        // 跳过添加行按钮所在行
        if (row.querySelector('button[title="添加行"]')) {
            return;
        }
        
        // 获取行中所有单元格
        const cells = row.querySelectorAll('td[contenteditable="true"]');
        let found = false;
        
        // 检查每个单元格是否包含搜索文本
        cells.forEach(cell => {
            if (cell.textContent.toLowerCase().includes(searchText)) {
                found = true;
            }
        });
        
        // 显示或隐藏行
        if (found) {
            row.style.display = '';
            // 高亮匹配文本
            highlightSearchText(cells, searchText);
        } else {
            row.style.display = 'none';
        }
    });
    
    // 显示搜索结果提示
    const hiddenRows = document.querySelectorAll('.editable-table tbody tr[style="display: none;"]');
    const totalRows = document.querySelectorAll('.editable-table tbody tr:not([style="display: none;"])').length - 1; // 减去添加行按钮行
    
    if (totalRows === 0) {
        showToast('未找到匹配的数据', 'error');
    } else {
        showToast(`找到 ${totalRows} 条匹配数据`, 'success');
    }
}

// 高亮搜索文本
function highlightSearchText(cells, searchText) {
    cells.forEach(cell => {
        const originalText = cell.textContent;
        const lowerText = originalText.toLowerCase();
        const index = lowerText.indexOf(searchText.toLowerCase());
        
        if (index >= 0) {
            // 高亮匹配文本
            const beforeText = originalText.substring(0, index);
            const matchedText = originalText.substring(index, index + searchText.length);
            const afterText = originalText.substring(index + searchText.length);
            
            // 使用临时容器，避免影响编辑功能
            const tempContainer = document.createElement('div');
            tempContainer.innerHTML = `${beforeText}<span class="search-highlight">${matchedText}</span>${afterText}`;
            
            // 应用样式
            const style = document.createElement('style');
            style.textContent = `
                .search-highlight {
                    background-color: rgba(255, 235, 59, 0.5);
                    border-radius: 3px;
                    padding: 0 2px;
                    font-weight: bold;
                }
                .dark-mode .search-highlight {
                    background-color: rgba(0, 209, 255, 0.3);
                }
            `;
            document.head.appendChild(style);
            
            // 应用高亮内容
            cell.innerHTML = tempContainer.innerHTML;
            
            // 恢复内容可编辑状态
            cell.addEventListener('focus', function() {
                this.textContent = originalText;
            }, { once: true });
        }
    });
}

// 存储当前用户信息
let currentUser = null;

// 显示当前用户信息
async function displayCurrentUser() {
    try {
        const token = getToken();
        if (!token) return;

        const response = await fetch('/api/validate-token', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.valid && data.user) {
                currentUser = data.user; // 存储用户信息

                const userInfoBanner = document.getElementById('userInfoBanner');
                const currentUserName = document.getElementById('currentUserName');

                if (userInfoBanner && currentUserName) {
                    currentUserName.textContent = data.user.username;
                    userInfoBanner.style.display = 'block';

                    // 如果是管理员，显示特殊标识
                    if (data.user.role === 'admin') {
                        currentUserName.innerHTML = `${data.user.username} <span style="background: #ff4757; color: white; padding: 2px 6px; border-radius: 8px; font-size: 10px; margin-left: 5px;">管理员</span>`;
                    }
                }

                // 根据用户角色控制创建者列的显示
                toggleCreatorColumnVisibility();
            }
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
    }
}

// 检查当前用户是否为管理员
function isCurrentUserAdmin() {
    return currentUser && currentUser.role === 'admin';
}

// 根据用户角色控制创建者列的显示
function toggleCreatorColumnVisibility() {
    const isAdmin = isCurrentUserAdmin();

    // 控制历史记录表格的创建者列头部
    const creatorHeader = document.getElementById('creatorHeader');
    if (creatorHeader) {
        creatorHeader.style.display = isAdmin ? 'table-cell' : 'none';
    }

    // 控制原始表格数据的创建者列头部
    const rawTableCreatorHeader = document.getElementById('rawTableCreatorHeader');
    if (rawTableCreatorHeader) {
        rawTableCreatorHeader.style.display = isAdmin ? 'table-cell' : 'none';
    }

    // 控制数据分析按钮的显示（仅管理员可见）
    const analyticsButton = document.getElementById('analyticsButton');
    if (analyticsButton) {
        analyticsButton.style.display = isAdmin ? 'block' : 'none';
    }
}

// 获取表格的列数（根据用户角色动态计算）
function getTableColspan() {
    // 基础列数：ID、日期、配件数量/总金额、操作
    // 历史记录表格：ID、日期、配件数量、总金额、操作 = 5列
    // 如果是管理员，还有创建者列 = 6列
    return isCurrentUserAdmin() ? 6 : 5;
}

// 获取原始表格数据的列数
function getRawTableColspan() {
    // 基础列数：ID、日期、总金额、数据预览、操作 = 5列
    // 如果是管理员，还有创建者列 = 6列
    return isCurrentUserAdmin() ? 6 : 5;
}

// 给搜索框添加回车键搜索功能
document.addEventListener('DOMContentLoaded', async function() {
    // 初始化主题
    initTheme();

    // 显示当前用户信息并等待完成
    await displayCurrentUser();

    // 页面加载时自动加载历史记录
    searchRecords(true); // 传入 true 参数表示静默加载，不显示加载提示

    // 延迟添加动画，确保页面功能正常加载
    setTimeout(() => {
        try {
            addPageLoadAnimations();
            enhanceUIInteractions();
        } catch (error) {
            console.warn('动画初始化失败:', error);
        }
    }, 100);

    // 添加表格搜索回车键功能
    const tableSearchInput = document.querySelector('#tableSearchInput');
    if (tableSearchInput) {
        tableSearchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                searchTable();
            }
        });
    }

    // 添加历史记录搜索回车键功能
    const searchInput = document.querySelector('#searchInput');
    if (searchInput) {
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                searchRecords();
            }
        });
    }
    
    // 添加原始表格数据搜索回车键功能
    const rawTableSearchInput = document.querySelector('#rawTableSearchInput');
    if (rawTableSearchInput) {
        rawTableSearchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                searchRawTables();
            }
        });
    }
    
    // 添加差价计算相关事件监听器
    const buchaAmountInput = document.getElementById('buchaAmount');
    if (buchaAmountInput) {
         buchaAmountInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                calculateBucha();
            } else if (this.value && parseInt(this.value) > 0) {
                // 输入金额后自动计算，但添加短暂延迟避免频繁计算
                clearTimeout(this.timer);
                this.timer = setTimeout(() => {
                    calculateBucha();
                }, 500);
            }
        });
    }
   
    const buchaCalculateBtn = document.getElementById('buchaCalculateBtn');
    if(buchaCalculateBtn) buchaCalculateBtn.addEventListener('click', calculateBucha);

    const buchaResetBtn = document.getElementById('buchaResetBtn');
    if(buchaResetBtn) buchaResetBtn.addEventListener('click', resetBucha);

    const buchaCopyBtn = document.getElementById('buchaCopyBtn');
    if(buchaCopyBtn) buchaCopyBtn.addEventListener('click', copyBucha);
    
    // 添加补差类型切换按钮事件
    const normalBuchaBtn = document.getElementById('normalBuchaBtn');
    if(normalBuchaBtn) normalBuchaBtn.addEventListener('click', () => toggleBuchaType('normal'));
    
    const bucha200Btn = document.getElementById('bucha200Btn');
    if(bucha200Btn) bucha200Btn.addEventListener('click', () => toggleBuchaType('bucha200'));
    
    const largeBuchaBtn = document.getElementById('largeBuchaBtn');
    if(largeBuchaBtn) largeBuchaBtn.addEventListener('click', () => toggleBuchaType('large'));
    
    // 添加邀请定制输入框回车键处理
    const machinePrice = document.getElementById('machinePrice');
    const extraPrice = document.getElementById('extraPrice');
    
    if(machinePrice) {
        machinePrice.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                calculateCustomTotal(true);
            }
        });
    }
    
    if(extraPrice) {
        extraPrice.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                calculateCustomTotal(true);
            }
        });
    }

    // --- 新增快捷键支持 ---
    const priceInput = document.getElementById('price');
    const configInput = document.getElementById('config');

    const handleEnterToAddItem = (event) => {
        if (event.key === 'Enter') {
            event.preventDefault(); // 防止触发表单提交等默认行为
            addNewLine();
        }
    };

    if (priceInput) priceInput.addEventListener('keyup', handleEnterToAddItem);
    if (configInput) configInput.addEventListener('keyup', handleEnterToAddItem);

    document.addEventListener('keydown', (event) => {
        // 快捷键：Ctrl/Cmd + Enter 复制结果
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            event.preventDefault();
            copyToClipboard();
            animateCSS('.calculate-button', 'pulse');
        }
    });
});

// 清除表格搜索
function clearTableSearch() {
    const searchInput = document.getElementById('tableSearchInput');
    searchInput.value = '';
    
    // 显示所有行
    const rows = document.querySelectorAll('.editable-table tbody tr');
    rows.forEach(row => {
        if (!row.querySelector('button[title="添加行"]')) {
            row.style.display = '';
            
            // 移除高亮
            const cells = row.querySelectorAll('td[contenteditable="true"]');
            cells.forEach(cell => {
                // 检查是否有搜索高亮
                if (cell.querySelector('.search-highlight')) {
                    // 还原原始文本内容
                    const originalText = cell.textContent;
                    cell.textContent = originalText;
                }
            });
        }
    });
    
    showToast('已清除搜索', 'success');
}

// 快速添加单个配件
function quickAdd(price, operationType, config) {
    if (!price || price <= 0) {
        showToast('价格不能为空或小于等于0', 'error');
        return;
    }
    
    if (!config) {
        showToast('配置描述不能为空', 'error');
        return;
    }
    
    let description = config;
    if (config === '风扇') {
        description = '1把' + config;
    } else if (config === '集线器') {
        description = '1个' + config;
    }

    // 构建新行
    const newLine = `+${price} ${operationType} ${description}`;
    const inputText = document.getElementById('inputText');
    
    // 添加到文本区域
    if (inputText.value && inputText.value.trim() !== '') {
        inputText.value += '\n' + newLine;
    } else {
        inputText.value = newLine;
    }
    
    // 自动计算总额
    autoCalculate();
    
    // 显示添加成功提示
    showToast(`已添加 ${description}`, 'success');
}

// 快速添加多个相同配件
function quickAddMultiple(unitPrice, operationType, config) {
    // 打开数量输入模态框
    document.getElementById('quantityModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // 保存当前项目信息到全局变量，以便在确认时使用
    tempUnitPrice = unitPrice;
    tempOperationType = operationType;
    tempConfig = config;
}

// 关闭数量输入模态框
function closeQuantityModal() {
    document.getElementById('quantityModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// 增加数量
function incrementQuantity() {
    const input = document.getElementById('quantityInput');
    input.value = parseInt(input.value) + 1;
}

// 减少数量
function decrementQuantity() {
    const input = document.getElementById('quantityInput');
    if (parseInt(input.value) > 1) {
        input.value = parseInt(input.value) - 1;
    }
}

// 设置指定数量
function setQuantity(number) {
    document.getElementById('quantityInput').value = number;
    applyQuantity();
}

// 应用选择的数量
function applyQuantity() {
    const quantity = parseInt(document.getElementById('quantityInput').value);
    
    if (isNaN(quantity) || quantity <= 0) {
        showToast('请输入有效数量', 'error');
        return;
    }
    
    // 使用之前保存的临时项目信息
    const totalPrice = tempUnitPrice * quantity;
    const inputText = document.getElementById('inputText');
    let newLine = '';
    
    const measureWord = (tempConfig === '风扇') ? '把' : '个';
    
    // 根据是否需要显示数量，构建不同的行
    if (quantity === 1) {
        newLine = `+${tempUnitPrice} ${tempOperationType} 1${measureWord}${tempConfig}`;
    } else {
        newLine = `+${totalPrice} ${tempOperationType} ${quantity}${measureWord}${tempConfig}`;
    }
    
    // 添加到文本区域
    if (inputText.value && inputText.value.trim() !== '') {
        inputText.value += '\n' + newLine;
    } else {
        inputText.value = newLine;
    }
    
    // 自动计算总额
    autoCalculate();
    
    // 关闭模态框
    closeQuantityModal();
    
    // 显示添加成功提示
    showToast(`已添加 ${quantity}${measureWord} ${tempConfig}`, 'success');
}

// 内部备注相关函数
function openInternalNotesModal() {
    const internalNotesModal = document.getElementById('internalNotesModal');
    internalNotesModal.style.display = 'block';
    document.body.style.overflow = 'hidden';

    // 简化动画
    try {
        animateCSS('#internalNotesModal .modal-content', 'fadeInUp').catch(() => {
            // 忽略动画错误
        });
    } catch (error) {
        // 忽略动画错误
    }
}

function closeInternalNotesModal() {
    const internalNotesModal = document.getElementById('internalNotesModal');

    // 直接关闭
    internalNotesModal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// 硬盘分区选择器更新函数
function updateDiskPartition() {
    const select = document.getElementById('diskPartitionSelect');
    const input = document.getElementById('diskPartitionInput');

    if (select && input && select.value) {
        input.value = select.value;
        // 重置选择器到默认状态
        select.selectedIndex = 0;
    }
}

function copyInternalNotes() {
    const table = document.getElementById('internalNotesTable');
    const rows = table.querySelectorAll('tr');
    let content = '';

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length === 2) {
            const label = cells[0].textContent.trim();
            const input = cells[1].querySelector('input');
            const value = input ? input.value.trim() : cells[1].textContent.trim();
            content += `${label}\t${value}；\n`;
        }
    });

    // 使用更兼容的复制方法
    function fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;

        // 避免滚动到底部
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showToast('内部备注已复制到剪贴板', 'success');
                closeInternalNotesModal(); // 复制成功后关闭模态框
            } else {
                showToast('复制失败，请手动复制', 'error');
            }
        } catch (err) {
            console.error('复制失败:', err);
            showToast('复制失败，请手动复制', 'error');
        }

        document.body.removeChild(textArea);
    }

    // 检查是否支持现代的clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(content).then(() => {
            showToast('内部备注已复制到剪贴板', 'success');
            closeInternalNotesModal(); // 复制成功后关闭模态框
        }).catch(err => {
            console.error('现代API复制失败，使用降级方案:', err);
            fallbackCopyTextToClipboard(content);
        });
    } else {
        // 直接使用降级方案
        fallbackCopyTextToClipboard(content);
    }
}
