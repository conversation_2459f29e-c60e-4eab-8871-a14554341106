// 快速添加单个配件
function quickAdd(price, operationType, config) {
    if (!price || price <= 0) {
        showToast('价格不能为空或小于等于0', 'error');
        return;
    }

    if (!config) {
        showToast('配置描述不能为空', 'error');
        return;
    }

    let description = config;
    if (config === '风扇') {
        description = '1把' + config;
    } else if (config === '集线器') {
        description = '1个' + config;
    }

    // 构建新行
    const newLine = `+${price} ${operationType} ${description}`;
    const inputText = document.getElementById('inputText');

    // 添加到文本区域
    if (inputText.value && inputText.value.trim() !== '') {
        inputText.value += '\n' + newLine;
    } else {
        inputText.value = newLine;
    }

    // 自动计算总额
    if (typeof autoCalculate === 'function') {
        autoCalculate();
    } else {
        console.warn('autoCalculate function not found');
    }

    // 显示添加成功提示
    showToast(`已添加 ${description}`, 'success');
}

// 抖音平台快速添加配件（不显示平台名称）
function quickAddDouyin(price, operationType, config) {
    if (!price || price <= 0) {
        showToast('价格不能为空或小于等于0', 'error');
        return;
    }

    if (!config) {
        showToast('配置描述不能为空', 'error');
        return;
    }

    let description = config;
    if (config === '风扇') {
        description = '1把' + config;
    } else if (config === '集线器') {
        description = '1个' + config;
    }

    // 构建新行（不包含平台信息）
    const newLine = `+${price} ${operationType} ${description}`;
    const inputText = document.getElementById('inputText');

    // 添加到文本区域
    if (inputText.value && inputText.value.trim() !== '') {
        inputText.value += '\n' + newLine;
    } else {
        inputText.value = newLine;
    }

    // 自动计算总额
    autoCalculate();

    // 显示添加成功提示
    showToast(`已添加 ${description}`, 'success');
}

// 快速添加多个相同配件
function quickAddMultiple(unitPrice, operationType, config) {
    // 打开数量输入模态框
    document.getElementById('quantityModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // 保存当前项目信息到全局变量，以便在确认时使用
    tempUnitPrice = unitPrice;
    tempOperationType = operationType;
    tempConfig = config;
}

// 关闭数量输入模态框
function closeQuantityModal() {
    document.getElementById('quantityModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// 增加数量
function incrementQuantity() {
    const input = document.getElementById('quantityInput');
    input.value = parseInt(input.value) + 1;
}

// 减少数量
function decrementQuantity() {
    const input = document.getElementById('quantityInput');
    if (parseInt(input.value) > 1) {
        input.value = parseInt(input.value) - 1;
    }
}

// 设置指定数量
function setQuantity(number) {
    document.getElementById('quantityInput').value = number;
    applyQuantity();
    closeQuantityModal();
}

// 应用选择的数量
function applyQuantity() {
    const quantity = parseInt(document.getElementById('quantityInput').value);
    
    if (isNaN(quantity) || quantity <= 0) {
        showToast('请输入有效数量', 'error');
        return;
    }
    
    // 使用之前保存的临时项目信息
    const totalPrice = tempUnitPrice * quantity;
    const inputText = document.getElementById('inputText');
    let newLine = '';
    
    const measureWord = (tempConfig === '风扇') ? '把' : '个';
    
    // 根据是否需要显示数量，构建不同的行
    if (quantity === 1) {
        newLine = `+${tempUnitPrice} ${tempOperationType} 1${measureWord}${tempConfig}`;
    } else {
        newLine = `+${totalPrice} ${tempOperationType} ${quantity}${measureWord}${tempConfig}`;
    }
    
    // 添加到文本区域
    if (inputText.value && inputText.value.trim() !== '') {
        inputText.value += '\n' + newLine;
    } else {
        inputText.value = newLine;
    }
    
    // 自动计算总额
    autoCalculate();
    
    // 关闭模态框
    closeQuantityModal();
    
    // 显示添加成功提示
    showToast(`已添加 ${quantity}${measureWord} ${tempConfig}`, 'success');
}

// 风扇安装位置处理函数
function processFanPosition() {
    // 获取各个位置的风扇数量
    const tailFans = parseInt(document.getElementById('tailFans').value) || 0;
    const topFans = parseInt(document.getElementById('topFans').value) || 0;
    const sideFans = parseInt(document.getElementById('sideFans').value) || 0;
    const frontFans = parseInt(document.getElementById('frontFans').value) || 0;
    const bottomFans = parseInt(document.getElementById('bottomFans').value) || 0;

    let description = '';
    let totalFans = 0;
    let positiveFans = 0; // 正装风扇数量
    let negativeFans = 0; // 反装风扇数量

    // 添加各个位置的风扇描述
    if (tailFans > 0) {
        description += `尾${tailFans}正 `;
        totalFans += tailFans;
        positiveFans += tailFans;
    }

    if (topFans > 0) {
        description += `上${topFans}正 `;
        totalFans += topFans;
        positiveFans += topFans;
    }

    if (sideFans > 0) {
        description += `侧${sideFans}反 `;
        totalFans += sideFans;
        negativeFans += sideFans;
    }

    if (frontFans > 0) {
        description += `前${frontFans}正 `;
        totalFans += frontFans;
        positiveFans += frontFans;
    }

    if (bottomFans > 0) {
        description += `下${bottomFans}反 `;
        totalFans += bottomFans;
        negativeFans += bottomFans;
    }

    // 如果没有输入任何风扇数量
    if (totalFans === 0) {
        showToast('请至少在一个位置输入风扇数量', 'error');
        return;
    }

    description = description.trim();

    // 构建新格式：总计在前，详情在后，同一行
    let summary = '';
    if (positiveFans > 0 && negativeFans > 0) {
        summary = `${positiveFans}正${negativeFans}反`;
    } else if (positiveFans > 0) {
        summary = `${positiveFans}正`;
    } else if (negativeFans > 0) {
        summary = `${negativeFans}反`;
    }

    const finalResult = summary + ' ' + description;

    // 更新实时反馈
    const resultPreview = document.getElementById('fanPositionResult');
    resultPreview.innerHTML = finalResult;
    resultPreview.style.display = 'block';

    // 使用更兼容的复制方法
    function fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;

        // 避免滚动到底部
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showToast('风扇位置信息已复制到剪贴板', 'success');
                closeFanPositionModal(); // 复制成功后关闭模态框
            } else {
                showToast('复制失败，请手动复制', 'error');
            }
        } catch (err) {
            console.error('复制失败:', err);
            showToast('复制失败，请手动复制', 'error');
        }

        document.body.removeChild(textArea);
    }

    // 检查是否支持现代的clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(finalResult).then(() => {
            showToast('风扇位置信息已复制到剪贴板', 'success');
            closeFanPositionModal(); // 复制成功后关闭模态框
        }).catch(err => {
            console.error('现代API复制失败，使用降级方案:', err);
            fallbackCopyTextToClipboard(finalResult);
        });
    } else {
        // 直接使用降级方案
        fallbackCopyTextToClipboard(finalResult);
    }
}

// 打开风扇位置选择模态框
function openFanPositionModal() {
    // 显示模态框
    document.getElementById('fanPositionModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // 清空输入框
    document.getElementById('tailFans').value = '';
    document.getElementById('topFans').value = '';
    document.getElementById('sideFans').value = '';
    document.getElementById('frontFans').value = '';
    document.getElementById('bottomFans').value = '';
    
    // 隐藏结果预览
    document.getElementById('fanPositionResult').style.display = 'none';
    
    // 添加实时预览事件监听器
    const fanInputs = ['tailFans', 'topFans', 'sideFans', 'frontFans', 'bottomFans'];
    fanInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        input.addEventListener('input', updateFanPositionPreview);
    });
    
    // 聚焦第一个输入框
    document.getElementById('tailFans').focus();
}

// 更新风扇位置预览
function updateFanPositionPreview() {
    // 获取各个位置的风扇数量
    const tailFans = parseInt(document.getElementById('tailFans').value) || 0;
    const topFans = parseInt(document.getElementById('topFans').value) || 0;
    const sideFans = parseInt(document.getElementById('sideFans').value) || 0;
    const frontFans = parseInt(document.getElementById('frontFans').value) || 0;
    const bottomFans = parseInt(document.getElementById('bottomFans').value) || 0;

    let description = '';
    let totalFans = 0;
    let positiveFans = 0; // 正装风扇数量
    let negativeFans = 0; // 反装风扇数量

    // 添加各个位置的风扇描述
    if (tailFans > 0) {
        description += `尾${tailFans}正 `;
        totalFans += tailFans;
        positiveFans += tailFans;
    }

    if (topFans > 0) {
        description += `上${topFans}正 `;
        totalFans += topFans;
        positiveFans += topFans;
    }

    if (sideFans > 0) {
        description += `侧${sideFans}反 `;
        totalFans += sideFans;
        negativeFans += sideFans;
    }

    if (frontFans > 0) {
        description += `前${frontFans}正 `;
        totalFans += frontFans;
        positiveFans += frontFans;
    }

    if (bottomFans > 0) {
        description += `下${bottomFans}反 `;
        totalFans += bottomFans;
        negativeFans += bottomFans;
    }

    description = description.trim();

    // 更新实时反馈
    const resultPreview = document.getElementById('fanPositionResult');
    if (totalFans > 0) {
        // 构建新格式：总计在前，详情在后，同一行
        let summary = '';
        if (positiveFans > 0 && negativeFans > 0) {
            summary = `${positiveFans}正${negativeFans}反`;
        } else if (positiveFans > 0) {
            summary = `${positiveFans}正`;
        } else if (negativeFans > 0) {
            summary = `${negativeFans}反`;
        }

        const finalResult = summary + ' ' + description;
        resultPreview.innerHTML = finalResult;
        resultPreview.style.display = 'block';
    } else {
        resultPreview.style.display = 'none';
    }
}

// 关闭风扇位置选择模态框
function closeFanPositionModal() {
    document.getElementById('fanPositionModal').style.display = 'none';
    document.body.style.overflow = 'auto';
} 